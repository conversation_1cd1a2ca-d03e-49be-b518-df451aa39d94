﻿@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@using DrugPrevention.BlazorApp.TuyenTM.Components.Account
@inject NavigationManager Navigation

<CascadingAuthenticationState>
    <Router AppAssembly="typeof(Program).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)">
                <NotAuthorized Context="authContext">
                    @if (authContext.User.Identity?.IsAuthenticated != true)
                    {
                        <Login />
                    }
                    else
                    {
                        <AccessDenied />
                    }
                </NotAuthorized>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="routeData" Selector="h1" />
        </Found>
    </Router>
</CascadingAuthenticationState>
