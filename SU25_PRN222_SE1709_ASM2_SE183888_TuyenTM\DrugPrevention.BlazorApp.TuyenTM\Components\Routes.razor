﻿@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@using DrugPrevention.BlazorApp.TuyenTM.Components.Account

<CascadingAuthenticationState>
    <Router AppAssembly="typeof(Program).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)">
                <NotAuthorized Context="authContext">
                    @if (authContext.User.Identity?.IsAuthenticated != true)
                    {
                        <RedirectToLogin />
                    }
                    else
                    {
                        <div class="alert alert-danger" role="alert">
                            <h4 class="alert-heading">Access Denied</h4>
                            <p>You are not authorized to access this resource.</p>
                            <hr>
                            <p class="mb-0">Please contact your administrator if you believe this is an error.</p>
                        </div>
                    }
                </NotAuthorized>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="routeData" Selector="h1" />
        </Found>
    </Router>
</CascadingAuthenticationState>
