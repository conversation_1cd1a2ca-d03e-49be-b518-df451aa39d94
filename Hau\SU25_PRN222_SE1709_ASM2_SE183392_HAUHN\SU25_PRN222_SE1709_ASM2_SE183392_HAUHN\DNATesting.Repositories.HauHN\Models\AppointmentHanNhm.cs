﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DNATesting.Repositories.HauHN.Models;

public partial class AppointmentHanNhm
{
    public string AppointmentHanNhmid { get; set; }

    public int UserAccountId { get; set; }

    public string Note { get; set; }

    public DateTime? CreatedAt { get; set; }

    public string ApplicationSource { get; set; }

    public virtual ICollection<AppointmentDetailHanNhm> AppointmentDetailHanNhms { get; set; } = new List<AppointmentDetailHanNhm>();

    public virtual ICollection<SamplePhuongMt> SamplePhuongMts { get; set; } = new List<SamplePhuongMt>();

    public virtual ICollection<TestResultHauHn> TestResultHauHns { get; set; } = new List<TestResultHauHn>();

    public virtual SystemUserAccount UserAccount { get; set; }

    public virtual ICollection<DnatestOptionKhanhHn> DnatestOptionKhanhHns { get; set; } = new List<DnatestOptionKhanhHn>();

    public virtual ICollection<ReportTinPt> ReportTinPts { get; set; } = new List<ReportTinPt>();
}