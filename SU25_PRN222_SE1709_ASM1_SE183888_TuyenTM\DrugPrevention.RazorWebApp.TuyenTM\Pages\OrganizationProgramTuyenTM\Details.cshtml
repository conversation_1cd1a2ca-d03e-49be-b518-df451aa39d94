﻿@page
@model DrugPrevention.RazorWebApp.TuyenTM.Pages.OrganizationProgramTuyenTM.DetailsModel

<div class="container mt-4">
    <h3 class="mb-4">Organization Program Details</h3>
    <hr />
    <div class="row mb-3">

        <div class="col-sm-4 fw-bold">Organization Program ID:</div>
        <div class="col-sm-8" id="OrganizationProgramTuyenTMID">@Model.OrganizationProgramsTuyenTM.OrganizationProgramTuyenTMID</div>
        
        <div class="col-sm-4 fw-bold">Joined Date:</div>
        <div class="col-sm-8" id="JoinedDate">@Model.OrganizationProgramsTuyenTM.JoinedDate.ToString("HH:mm dd/MM/yyyy")</div>

        <div class="col-sm-4 fw-bold">Contribution Description:</div>
        <div class="col-sm-8" id="ContributionDescription">@Model.OrganizationProgramsTuyenTM.ContributionDescription</div>

        <div class="col-sm-4 fw-bold">Is Sponsor:</div>
        <div class="col-sm-8" id="IsSponsor">@((Model.OrganizationProgramsTuyenTM.IsSponsor == true) ? "Yes" : "No")</div>

        <div class="col-sm-4 fw-bold">Is Organizer:</div>
        <div class="col-sm-8" id="IsOrganizer">@((Model.OrganizationProgramsTuyenTM.IsOrganizer == true) ? "Yes" : "No")</div>

        <div class="col-sm-4 fw-bold">Program Role:</div>
        <div class="col-sm-8" id="ProgramRole">@Model.OrganizationProgramsTuyenTM.ProgramRole</div>

        <div class="col-sm-4 fw-bold">Funding Amount:</div>
        <div class="col-sm-8" id="FundingAmount">@Model.OrganizationProgramsTuyenTM.FundingAmount?.ToString("C")</div>

        <div class="col-sm-4 fw-bold">Feedback:</div>
        <div class="col-sm-8" id="Feedback">@Model.OrganizationProgramsTuyenTM.Feedback</div>

        <div class="col-sm-4 fw-bold">Last Updated:</div>
        <div class="col-sm-8" id="LastUpdated">@Model.OrganizationProgramsTuyenTM.LastUpdated?.ToString("HH:mm dd/MM/yyyy")</div>

        <div class="col-sm-4 fw-bold">Organization:</div>
        <div class="col-sm-8" id="OrganizationName">@Model.OrganizationProgramsTuyenTM.Organization?.OrganizationName</div>

        <div class="col-sm-4 fw-bold">Program:</div>
        <div class="col-sm-8" id="ProgramName">@Model.OrganizationProgramsTuyenTM.ProgramToanNS?.ProgramName</div>
    </div>

    <div class="row mt-4">
        <div class="col-sm-4"></div>
        <div class="col-sm-8">
            <a asp-page="./Edit" asp-route-id="@Model.OrganizationProgramsTuyenTM.OrganizationProgramTuyenTMID" class="btn btn-primary me-2">Edit</a>
            <a asp-page="./Index" class="btn btn-secondary">Back to List</a>
        </div>
    </div>
</div>
<script src="~/js/signalr/dist/browser/signalr.js"></script>
<script>
    "use strict";

    var connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

    connection.start().then(function () {
        console.log("Connected to the SignalR Hub");
    }).catch(function (err) {
        return console.error(err.toString());
    });

    // connection.on("Receiver_DeleteOrganizationTuyenTM", function (OrganizationProgramTuyenTMID) {
    //     alert($(`#${UserTuyenTMID}`).text());
    //     $('#UserTuyenTMBodyID').find(`tr[id='${UserTuyenTMID}']`).remove();
    //     document.getElementById("OrganizationProgramTuyenTMID").innerHTML = "This item had deleted";
    // });

        connection.on("Receiver_DeleteOrganizationTuyenTM", function (deletedId) {
        const currentId = document.getElementById("OrganizationProgramTuyenTMID")?.textContent?.trim();
        if (currentId === deletedId.toString()) {
            const fieldDefaults = {
                OrganizationProgramTuyenTMID: "0",             // số
                JoinedDate: "",                                // chuỗi
                ContributionDescription: "",                   // chuỗi
                IsSponsor: "false",                            // boolean
                IsOrganizer: "false",                          // boolean
                ProgramRole: "",                               // chuỗi
                FundingAmount: "0",                            // số
                Feedback: "",                                  // chuỗi
                LastUpdated: "",                               // chuỗi (hoặc ngày)
                OrganizationName: "",                          // chuỗi
                ProgramName: ""                                // chuỗi
            };

            Object.entries(fieldDefaults).forEach(([id, defaultValue]) => {
                const el = document.getElementById(id);
                if (el) {
                    el.textContent = defaultValue;
                }
            });

            // Optional: disable các nút hành động
            document.querySelector("a.btn-primary.me-2")?.classList.add("disabled");
            document.querySelector("a.btn-danger")?.classList.add("disabled");

            // Optional: hiển thị thông báo
            alert("This Organization Program has been deleted by another user.");
        }
    });



    connection.on("Receiver_UpdateOrganizationProgramTuyenTM", function(item) {
        console.log("item: ", item);
		document.getElementById("OrganizationProgramTuyenTMID").innerHTML = item.organizationProgramTuyenTMID;
        document.getElementById("JoinedDate").innerHTML = item.joinedDate ? new Date(item.joinedDate).toLocaleString('vi-VN') : "N/A";
		document.getElementById("ContributionDescription").innerHTML = item.contributionDescription || "N/A";
		document.getElementById("IsSponsor").innerHTML = item.isSponsor ? "Yes" : "No";
        document.getElementById("IsOrganizer").innerHTML = item.isOrganizer ? "Yes" : "No";
        document.getElementById("ProgramRole").innerHTML = item.programRole || "N/A";
        document.getElementById("FundingAmount").innerHTML = item.fundingAmount ? item.fundingAmount.toLocaleString('vi-VN', { style: 'currency', currency: 'VND' }) : "N/A";
        document.getElementById("Feedback").innerHTML = item.feedback || "N/A";
        document.getElementById("LastUpdated").innerHTML = item.lastUpdated ? new Date(item.lastUpdated).toLocaleString('vi-VN') : "N/A";
		document.getElementById("OrganizationName").innerHTML = item.organizationName || "N/A";
		document.getElementById("ProgramName").innerHTML = item.programName || "N/A";

        alert(item);
    })

</script>
