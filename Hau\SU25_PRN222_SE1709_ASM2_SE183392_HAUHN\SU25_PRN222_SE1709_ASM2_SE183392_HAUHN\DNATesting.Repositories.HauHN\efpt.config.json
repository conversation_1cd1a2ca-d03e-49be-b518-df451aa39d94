﻿{
   "CodeGenerationMode": 4,
   "ContextClassName": "SE18_PRN222_SE1709_G3_DNA_TestingContext",
   "ContextNamespace": null,
   "FilterSchemas": false,
   "IncludeConnectionString": true,
   "ModelNamespace": null,
   "OutputContextPath": null,
   "OutputPath": "Models",
   "PreserveCasingWithRegex": true,
   "ProjectRootNamespace": "DNATesting.Repositories.HauHN",
   "Schemas": null,
   "SelectedHandlebarsLanguage": 2,
   "SelectedToBeGenerated": 0,
   "T4TemplatePath": null,
   "Tables": [
      {
         "Name": "[dbo].[AppointmentDetailHanNHM]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[AppointmentHanNHM]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[AppointmentOptionMapping]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DNATestOptionDetailKhanhHN]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[DNATestOptionKhanhHN]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[FeedbackHoangDN]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ProcessLogPhuongMT]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ReportAppointmentMapping]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ReportDetailTinPT]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[ReportTinPT]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[SamplePhuongMT]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[System.UserAccount]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TestParticipantsHauHN]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TestParticipantsMapping]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TestResultDetailsHauHN]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TestResultHauHN]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[TestResultSampleMapping]",
         "ObjectType": 0
      },
      {
         "Name": "[dbo].[UserRole]",
         "ObjectType": 0
      }
   ],
   "UiHint": null,
   "UncountableWords": null,
   "UseAsyncStoredProcedureCalls": true,
   "UseBoolPropertiesWithoutDefaultSql": false,
   "UseDatabaseNames": false,
   "UseDatabaseNamesForRoutines": true,
   "UseDateOnlyTimeOnly": true,
   "UseDbContextSplitting": false,
   "UseDecimalDataAnnotationForSprocResult": true,
   "UseFluentApiOnly": true,
   "UseHandleBars": false,
   "UseHierarchyId": false,
   "UseInflector": true,
   "UseInternalAccessModifiersForSprocsAndFunctions": false,
   "UseLegacyPluralizer": false,
   "UseManyToManyEntity": false,
   "UseNoDefaultConstructor": false,
   "UseNoNavigations": false,
   "UseNoObjectFilter": false,
   "UseNodaTime": false,
   "UseNullableReferences": false,
   "UsePrefixNavigationNaming": false,
   "UseSchemaFolders": false,
   "UseSchemaNamespaces": false,
   "UseSpatial": false,
   "UseT4": false,
   "UseT4Split": false
}