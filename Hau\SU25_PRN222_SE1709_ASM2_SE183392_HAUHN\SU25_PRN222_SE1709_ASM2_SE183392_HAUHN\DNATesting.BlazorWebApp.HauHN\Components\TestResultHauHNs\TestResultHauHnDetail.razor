﻿@page "/TestResultHauHNs/TestResultHauHnDetail/{ResultHauHnid}"
@using DNATesting.Repositories.HauHN.Models
@using Microsoft.AspNetCore.Authorization

@attribute [Authorize(Roles = "1,2")]

<div class="medical-container">
    <div class="container-fluid p-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="page-title">
                        <div class="d-flex align-items-center">
                            <a href="/TestResultHauHNs/TestResultHauHnList" class="btn btn-light-secondary me-3">
                                <i class="bi bi-arrow-left"></i>
                            </a>
                            <div>
                                <h2 class="mb-0">Test Result Details</h2>
                                <p class="text-muted mb-0">View detailed information about the DNA test result</p>
                            </div>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-light-warning me-2">
                            <i class="bi bi-printer me-2"></i>Print Report
                        </button>
                        <a href="/TestResultHauHNs/TestResultHauHnForm/@ResultHauHnid" class="btn btn-primary">
                            <i class="bi bi-pencil-square me-2"></i>Edit Result
                        </a>
                    </div>
                </div>
            </div>
        </div>

        @if (testResultHauHn == null)
        {
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <p class="loading-text">Loading test result details...</p>
            </div>
        }
        else
        {
            <div class="row g-4">
                <!-- Status Overview -->
                <div class="col-12">
                    <div class="card status-overview">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <div class="status-item">
                                        <div class="status-icon @(testResultHauHn.IsVerified ? "verified" : "pending")">
                                            <i class="bi @(testResultHauHn.IsVerified ? "bi-check-circle-fill" : "bi-clock-fill")"></i>
                                        </div>
                                        <div class="status-info">
                                            <h6 class="text-muted mb-1">Status</h6>
                                            <h4 class="mb-0">@(testResultHauHn.IsVerified ? "Verified" : "Pending")</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="status-item">
                                        <div class="status-icon info">
                                            <i class="bi bi-calendar-check"></i>
                                        </div>
                                        <div class="status-info">
                                            <h6 class="text-muted mb-1">Test Date</h6>
                                            <h4 class="mb-0">@testResultHauHn.TestDate?.ToString("dd/MM/yyyy")</h4>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="status-item">
                                        <div class="status-icon info">
                                            <i class="bi bi-fingerprint"></i>
                                        </div>
                                        <div class="status-info">
                                            <h6 class="text-muted mb-1">Result ID</h6>
                                            <h4 class="mb-0">#@testResultHauHn.ResultHauHnid</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Test Information -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-medical fs-4 me-2 text-primary"></i>
                                <h5 class="mb-0">Test Information</h5>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <div class="info-icon">
                                            <i class="bi bi-calendar-event"></i>
                                        </div>
                                        <div class="info-content">
                                            <label>Appointment ID</label>
                                            <div class="value">@testResultHauHn.AppointmentHanNhmid</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <div class="info-icon">
                                            <i class="bi bi-virus"></i>
                                        </div>
                                        <div class="info-content">
                                            <label>Test Type</label>
                                            <div class="value">
                                                <span class="badge medical-badge-type">
                                                    @testResultHauHn.TestType
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <div class="info-icon">
                                            <i class="bi bi-clipboard-data"></i>
                                        </div>
                                        <div class="info-content">
                                            <label>Result Value</label>
                                            <div class="value fw-medium">@testResultHauHn.ResultValue</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <div class="info-icon">
                                            <i class="bi bi-calendar2-check"></i>
                                        </div>
                                        <div class="info-content">
                                            <label>Result Date</label>
                                            <div class="value">@testResultHauHn.ResultDate?.ToString("dd/MM/yyyy")</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <div class="info-icon">
                                            <i class="bi bi-calendar-check"></i>
                                        </div>
                                        <div class="info-content">
                                            <label>Test Date</label>
                                            <div class="value">@testResultHauHn.TestDate?.ToString("dd/MM/yyyy")</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <div class="info-icon">
                                            <i class="bi bi-bug"></i>
                                        </div>
                                        <div class="info-content">
                                            <label>Genetic Mutation</label>
                                            <div class="value">@testResultHauHn.GeneticMutation</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="info-item">
                                        <div class="info-icon">
                                            <i class="bi bi-activity"></i>
                                        </div>
                                        <div class="info-content">
                                            <label>Disease Risk</label>
                                            <div class="value">@testResultHauHn.DiseaseRisk</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="info-item">
                                        <div class="info-icon">
                                            <i class="bi bi-graph-up"></i>
                                        </div>
                                        <div class="info-content">
                                            <label>Relationship Probability</label>
                                                <div class="probability-value">
                                                    <span class="h4 mb-0">@testResultHauHn.RelationshipProbability%</span>
                                                </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Technician Information -->
                <div class="col-md-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-person-badge fs-4 me-2 text-primary"></i>
                                <h5 class="mb-0">Technician Information</h5>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="technician-info">
                                <div class="info-item">
                                    <div class="info-icon">
                                        <i class="bi bi-person-badge"></i>
                                    </div>
                                    <div class="info-content">
                                        <label>Technician ID</label>
                                        <div class="value fw-medium">@testResultHauHn.TechnicianId</div>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-icon">
                                        <i class="bi bi-person-check"></i>
                                    </div>
                                    <div class="info-content">
                                        <label>Verified By</label>
                                        <div class="value">@testResultHauHn.VerifiedBy</div>
                                    </div>
                                </div>
                                <div class="verification-status mt-4">
                                    @if (testResultHauHn.IsVerified)
                                    {
                                        <span class="badge medical-badge-verified">
                                            <i class="bi bi-shield-check me-1"></i>Result Verified
                                        </span>
                                    }
                                    else
                                    {
                                        <span class="badge medical-badge-pending">
                                            <i class="bi bi-shield-exclamation me-1"></i>Pending Verification
                                        </span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

<style>
    .medical-container {
        background-color: #f0f4f8;
        min-height: 100vh;
    }

    .page-title h2 {
        color: #1a365d;
        font-weight: 700;
        letter-spacing: -0.5px;
    }

    .btn-light-secondary {
        color: #4a5568;
        background-color: #edf2f7;
        border: 1px solid #e2e8f0;
        font-weight: 500;
    }

    .btn-light-secondary:hover {
        background-color: #e2e8f0;
        color: #2d3748;
    }

    .btn-light-warning {
        color: #c05621;
        background-color: #fffaf0;
        border: 1px solid #feebc8;
        font-weight: 500;
    }

    .btn-light-warning:hover {
        background-color: #c05621;
        color: white;
        border-color: #c05621;
    }

    .card {
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        background: #ffffff;
    }

    .status-overview {
        background: linear-gradient(135deg, #2b6cb0 0%, #2c5282 100%);
        color: white;
    }

    .status-item {
        display: flex;
        align-items: center;
        padding: 1rem;
    }

    .status-icon {
        width: 48px;
        height: 48px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-right: 1rem;
    }

    .status-icon.verified {
        background-color: rgba(56, 161, 105, 0.2);
        color: #38a169;
    }

    .status-icon.pending {
        background-color: rgba(214, 158, 46, 0.2);
        color: #d69e2e;
    }

    .status-icon.info {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
    }

    .status-info h6 {
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
    }

    .card-header {
        background: white;
        border-bottom: 2px solid #e2e8f0;
        padding: 1.5rem;
    }

    .info-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1.5rem;
    }

    .info-icon {
        width: 40px;
        height: 40px;
        border-radius: 6px;
        background-color: #ebf8ff;
        color: #2b6cb0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        margin-right: 1rem;
    }

    .info-content {
        flex: 1;
    }

    .info-content label {
        display: block;
        color: #4a5568;
        font-size: 0.875rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .info-content .value {
        font-size: 1rem;
        color: #2d3748;
        font-weight: 500;
    }

    .medical-badge-type {
        background-color: #2b6cb0;
        color: white;
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
        border-radius: 4px;
        font-weight: 500;
    }

    .medical-badge-verified {
        background-color: #38a169;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-weight: 500;
    }

    .medical-badge-pending {
        background-color: #d69e2e;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-weight: 500;
    }

    .probability-container {
        background: #f8fafc;
        padding: 1.5rem;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
    }

    .probability-value {
        display: flex;
        align-items: baseline;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .technician-info {
        padding: 0.5rem;
    }

    .verification-status {
        text-align: center;
    }

    .loading-container {
        padding: 4rem;
        text-align: center;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e2e8f0;
        border-top: 3px solid #2b6cb0;
        border-radius: 50%;
        margin: 0 auto;
        -webkit-animation: spinner 1s linear infinite;
        animation: spinner 1s linear infinite;
    }

    @@-webkit-keyframes spinner {
        0% { -webkit-transform: rotate(0deg); }
        100% { -webkit-transform: rotate(360deg); }
    }

    @@keyframes spinner {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-text {
        color: #4a5568;
        margin-top: 1rem;
        font-size: 0.875rem;
        font-weight: 500;
    }
</style>

@code {
    [Parameter]
    public string ResultHauHnid { get; set; }
    
    private TestResultHauHn testResultHauHn;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            testResultHauHn = await ServiceProviders.testResultHauHNService.GetIdAsync(ResultHauHnid);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading test result: {ex.Message}");
        }
    }
}
