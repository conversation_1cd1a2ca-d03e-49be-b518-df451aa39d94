﻿using DNATesting.Repositories.HauHN;
using DNATesting.Repositories.HauHN.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.HauHN
{
    public class SystemUserAccountService : ISystemUserAccountService
    {
        private readonly UnitOfWork unitOfWork;
        public SystemUserAccountService() => unitOfWork = new UnitOfWork();

        public async Task<List<SystemUserAccount>> GetAllUserAccountAsync()
        {
            return await unitOfWork.SystemUserAccountRepository.GetAllAsync();
        }

        public async Task<SystemUserAccount> GetUserAccountAsync(string userName, string password)
        {
            return await unitOfWork.SystemUserAccountRepository.GetUserAccountAsync(userName, password);
        }

        public async Task<SystemUserAccount> GetUserAccountByUserNameAsync(string userName)
        {
            return await unitOfWork.SystemUserAccountRepository.GetUserAccountByUserNameAsync(userName);
        }
    }
}
