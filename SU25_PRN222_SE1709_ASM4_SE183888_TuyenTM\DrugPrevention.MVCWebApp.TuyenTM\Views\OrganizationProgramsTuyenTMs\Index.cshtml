﻿@model IEnumerable<DrugPrevention.Repositories.TuyenTM.Models.OrganizationProgramsTuyenTM>

@{
    ViewData["Title"] = "Organization Programs";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-handshake mr-2"></i>Organization Programs
                        </h4>
                        <a asp-action="Create" class="btn btn-light btn-sm">
                            <i class="fas fa-plus mr-1"></i>Create New
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped mb-0">
                            <thead class="table-dark">
                                <tr>
                                    <th class="text-center">
                                        <i class="fas fa-calendar mr-1"></i>Joined Date
                                    </th>
                                    <th>
                                        <i class="fas fa-comment mr-1"></i>Contribution
                                    </th>
                                    <th class="text-center">
                                        <i class="fas fa-dollar-sign mr-1"></i>Sponsor
                                    </th>
                                    <th class="text-center">
                                        <i class="fas fa-users mr-1"></i>Organizer
                                    </th>
                                    <th>
                                        <i class="fas fa-user-tag mr-1"></i>Role
                                    </th>
                                    <th class="text-right">
                                        <i class="fas fa-money-bill mr-1"></i>Funding
                                    </th>
                                    <th>
                                        <i class="fas fa-star mr-1"></i>Feedback
                                    </th>
                                    <th class="text-center">
                                        <i class="fas fa-clock mr-1"></i>Last Updated
                                    </th>
                                    <th>
                                        <i class="fas fa-building mr-1"></i>Organization
                                    </th>
                                    <th>
                                        <i class="fas fa-project-diagram mr-1"></i>Program
                                    </th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model) {
                                    <tr>
                                        <td class="text-center">
                                            <span class="badge bg-info">
                                                @item.JoinedDate.ToString("MMM dd, yyyy")
                                            </span>
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 200px;" title="@item.ContributionDescription">
                                                @Html.DisplayFor(modelItem => item.ContributionDescription)
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            @if(item.IsSponsor == true) {
                                                <span class="badge bg-success"><i class="fas fa-check"></i> Yes</span>
                                            } else if(item.IsSponsor == false) {
                                                <span class="badge bg-secondary"><i class="fas fa-times"></i> No</span>
                                            } else {
                                                <span class="badge bg-light text-dark">N/A</span>
                                            }
                                        </td>
                                        <td class="text-center">
                                            @if(item.IsOrganizer == true) {
                                                <span class="badge bg-success"><i class="fas fa-check"></i> Yes</span>
                                            } else if(item.IsOrganizer == false) {
                                                <span class="badge bg-secondary"><i class="fas fa-times"></i> No</span>
                                            } else {
                                                <span class="badge bg-light text-dark">N/A</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">@Html.DisplayFor(modelItem => item.ProgramRole)</span>
                                        </td>
                                        <td class="text-right">
                                            @if(item.FundingAmount.HasValue) {
                                                <span class="text-success font-weight-bold">$@item.FundingAmount.Value.ToString("N2")</span>
                                            } else {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="text-truncate" style="max-width: 150px;" title="@item.Feedback">
                                                @Html.DisplayFor(modelItem => item.Feedback)
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            @if(item.LastUpdated.HasValue) {
                                                <small class="text-muted">@item.LastUpdated.Value.ToString("MMM dd, yyyy")</small>
                                            } else {
                                                <small class="text-muted">-</small>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge bg-warning text-dark">
                                                @Html.DisplayFor(modelItem => item.Organization.OrganizationName)
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                @Html.DisplayFor(modelItem => item.ProgramToanNS.ProgramName)
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <a asp-action="Details" asp-route-id="@item.OrganizationProgramTuyenTMID"
                                               class="btn btn-info btn-sm mr-1" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a asp-action="Edit" asp-route-id="@item.OrganizationProgramTuyenTMID"
                                               class="btn btn-warning btn-sm mr-1" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.OrganizationProgramTuyenTMID"
                                               class="btn btn-danger btn-sm" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
                @if (!Model.Any())
                {
                    <div class="card-body text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No organization programs found</h5>
                        <p class="text-muted">Get started by creating your first organization program.</p>
                        <a asp-action="Create" class="btn btn-primary">
                            <i class="fas fa-plus mr-1"></i>Create New Program
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>
