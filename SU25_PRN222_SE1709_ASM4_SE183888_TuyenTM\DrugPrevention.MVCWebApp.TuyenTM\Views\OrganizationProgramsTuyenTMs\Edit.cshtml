﻿@model DrugPrevention.Repositories.TuyenTM.Models.OrganizationProgramsTuyenTM

@{
    ViewData["Title"] = "Edit Organization Program";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Organization Program
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="Edit">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                        <input type="hidden" asp-for="OrganizationProgramTuyenTMID" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="OrganizationID" class="form-label">
                                        <i class="fas fa-building me-1"></i>Organization
                                    </label>
                                    <select asp-for="OrganizationID" class="form-select" asp-items="ViewBag.OrganizationID">
                                        <option value="">-- Select Organization --</option>
                                    </select>
                                    <span asp-validation-for="OrganizationID" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ProgramToanNSID" class="form-label">
                                        <i class="fas fa-project-diagram me-1"></i>Program
                                    </label>
                                    <select asp-for="ProgramToanNSID" class="form-select" asp-items="ViewBag.ProgramToanNSID">
                                        <option value="">-- Select Program --</option>
                                    </select>
                                    <span asp-validation-for="ProgramToanNSID" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="JoinedDate" class="form-label">
                                        <i class="fas fa-calendar me-1"></i>Joined Date
                                    </label>
                                    <input asp-for="JoinedDate" class="form-control" type="date" disabled/>
                                    <span asp-validation-for="JoinedDate" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ProgramRole" class="form-label">
                                        <i class="fas fa-user-tag me-1"></i>Program Role
                                    </label>
                                    <input asp-for="ProgramRole" class="form-control" placeholder="Enter role in program" />
                                    <span asp-validation-for="ProgramRole" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="ContributionDescription" class="form-label">
                                <i class="fas fa-comment me-1"></i>Contribution Description
                            </label>
                            <textarea asp-for="ContributionDescription" class="form-control" rows="3"
                                      placeholder="Describe the contribution to this program..."></textarea>
                            <span asp-validation-for="ContributionDescription" class="text-danger"></span>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="IsSponsor" class="form-label">
                                        <i class="fas fa-dollar-sign me-1"></i>Is Sponsor
                                    </label>
                                    <select asp-for="IsSponsor" class="form-select">
                                        <option value="">-- Select --</option>
                                        <option value="true">Yes</option>
                                        <option value="false">No</option>
                                    </select>
                                    <span asp-validation-for="IsSponsor" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="IsOrganizer" class="form-label">
                                        <i class="fas fa-users me-1"></i>Is Organizer
                                    </label>
                                    <select asp-for="IsOrganizer" class="form-select">
                                        <option value="">-- Select --</option>
                                        <option value="true">Yes</option>
                                        <option value="false">No</option>
                                    </select>
                                    <span asp-validation-for="IsOrganizer" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="FundingAmount" class="form-label">
                                        <i class="fas fa-money-bill me-1"></i>Funding Amount
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input asp-for="FundingAmount" class="form-control" type="number" step="0.01" min="0" placeholder="0.00" />
                                    </div>
                                    <span asp-validation-for="FundingAmount" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label asp-for="Feedback" class="form-label">
                                        <i class="fas fa-star me-1"></i>Feedback
                                    </label>
                                    <textarea asp-for="Feedback" class="form-control" rows="3"
                                              placeholder="Enter feedback about this program participation..."></textarea>
                                    <span asp-validation-for="Feedback" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="LastUpdated" class="form-label">
                                        <i class="fas fa-clock me-1"></i>Last Updated
                                    </label>
                                    <input asp-for="LastUpdated" class="form-control" type="datetime-local" disabled />
                                    <span asp-validation-for="LastUpdated" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to List
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-1"></i>Update Program
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
