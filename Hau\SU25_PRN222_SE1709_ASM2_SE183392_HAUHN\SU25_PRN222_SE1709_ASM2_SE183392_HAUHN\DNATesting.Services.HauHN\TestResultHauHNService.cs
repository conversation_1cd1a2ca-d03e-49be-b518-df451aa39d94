﻿using DNATesting.Repositories.HauHN;
using DNATesting.Repositories.HauHN.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Transactions;

namespace DNATesting.Services.HauHN
{
    public class TestResultHauHNService : ITestResultHauHNService
    {
        private readonly UnitOfWork unitOfWork;
        public TestResultHauHNService() => unitOfWork ??= new UnitOfWork();

        public async Task<int> CreateAsync(TestResultHauHn result)
        {
            return await unitOfWork.TestResultHauHnRepository.CreateAsync(result);
        }

        public async Task<bool> DeleteAsync(string result)
        {
            var ResultNeedToRemove = await unitOfWork.TestResultHauHnRepository.GetByIdAsync(result);
            if (ResultNeedToRemove != null)
            {
                return await unitOfWork.TestResultHauHnRepository.RemoveAsync(ResultNeedToRemove);
            }
            return false;
        }

        public async Task<List<TestResultHauHn>> GetAllAsync()
        {
            return await unitOfWork.TestResultHauHnRepository.GetAllAsync();
        }

        public async Task<TestResultHauHn> GetIdAsync(string id)
        {
            return await unitOfWork.TestResultHauHnRepository.GetByIdAsync(id);
        }

        public async Task<List<TestResultHauHn>> SearchAsync(string code, bool? isVerified, string apID)
        {
            return await unitOfWork.TestResultHauHnRepository.SearchAsync(code,isVerified, apID);
        }

        public Task<bool> TestResultHauHnExistsAsync(string id)
        {
            return unitOfWork.TestResultHauHnRepository.TestResultHauHnExistsAsync(id);
        }

        public Task<int> UpdateAsync(TestResultHauHn result)
        {
            return unitOfWork.TestResultHauHnRepository.UpdateAsync(result);
        }
       
    }
}
