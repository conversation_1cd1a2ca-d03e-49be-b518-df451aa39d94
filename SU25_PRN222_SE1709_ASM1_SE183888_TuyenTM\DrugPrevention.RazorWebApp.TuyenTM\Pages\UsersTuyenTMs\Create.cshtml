﻿@page
@model DrugPrevention.RazorWebApp.TuyenTM.Pages.UsersTuyenTMs.CreateModel

@{
    ViewData["Title"] = "Create";
}

<h1>Create</h1>

<h4>UsersTuyenTM</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="UsersTuyenTM.Username" class="control-label"></label>
                <input asp-for="UsersTuyenTM.Username" class="form-control" />
                <span asp-validation-for="UsersTuyenTM.Username" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="UsersTuyenTM.Password" class="control-label"></label>
                <input asp-for="UsersTuyenTM.Password" class="form-control" />
                <span asp-validation-for="UsersTuyenTM.Password" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="UsersTuyenTM.Email" class="control-label"></label>
                <input asp-for="UsersTuyenTM.Email" class="form-control" />
                <span asp-validation-for="UsersTuyenTM.Email" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="UsersTuyenTM.FirstName" class="control-label"></label>
                <input asp-for="UsersTuyenTM.FirstName" class="form-control" />
                <span asp-validation-for="UsersTuyenTM.FirstName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="UsersTuyenTM.LastName" class="control-label"></label>
                <input asp-for="UsersTuyenTM.LastName" class="form-control" />
                <span asp-validation-for="UsersTuyenTM.LastName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="UsersTuyenTM.Role" class="control-label"></label>
                <select asp-for="UsersTuyenTM.Role" class="form-control">
                    <option>Admin</option>
                    <option>Manager</option>
                    <option>Consultant</option>
                    <option>Staff</option>
                    <option>Member</option>
                    <option>Guest</option>
                </select>
                <span asp-validation-for="UsersTuyenTM.Role" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="UsersTuyenTM.PhoneNumber" class="control-label"></label>
                <input asp-for="UsersTuyenTM.PhoneNumber" class="form-control" />
                <span asp-validation-for="UsersTuyenTM.PhoneNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
            <div>
                <input type="button" id="btnHubCreate" value="Create by SignalRHub" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-page="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}

<script src="~/js/signalr/dist/browser/signalr.js"></script>
<script>
    "use strict";

    var connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

    connection.start().then(function () {
        console.log("Connected to the SignalR Hub");
    }).catch(function (err) {
        return console.error(err.toString());
    });

    document.getElementById("btnHubCreate").addEventListener("click", function (event) {
    
    var dataObj = JSON.stringify({
                
            //int tự tăng thì để null
            // UserTuyenTMID: document.getElementById("UsersTuyenTM_UserTuyenTMID").value,
            Username: document.getElementById("UsersTuyenTM_Username").value,
            Email: document.getElementById("UsersTuyenTM_Email").value,
            FirstName: document.getElementById("UsersTuyenTM_FirstName").value,
            LastName: document.getElementById("UsersTuyenTM_LastName").value,
            Role: document.getElementById("UsersTuyenTM_Role").value,
            RegistrationDate: document.getElementById("UsersTuyenTM_RegistrationDate").value,
            // IsActive: document.getElementById("UsersTuyenTM_IsActive").value
            // PhoneNumber: document.getElementById("UsersTuyenTM_PhoneNumber").value

        });

        console.log(dataObj);

        connection.invoke("HubCreate_UserTuyenTM", dataObj).catch(function (err) {
            return console.error(err.toString());
        });
        event.preventDefault();
          });
</script>

