﻿using DNATesting.Repositories.HauHN.Basic;
using DNATesting.Repositories.HauHN.DBContext;
using DNATesting.Repositories.HauHN.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Repositories.HauHN
{
    public class SystemUserAccountRepository : GenericRepository<SystemUserAccount>
    {
        public SystemUserAccountRepository() { }
        public SystemUserAccountRepository(SE18_PRN222_SE1709_G3_DNA_TestingContext context) => _context = context;

        public async Task<SystemUserAccount> GetUserAccountAsync(string userName, string password)
        {
            return await _context.SystemUserAccounts.FirstOrDefaultAsync(u => u.UserName == userName && u.Password == password && u.IsActive == true);
        }
        public async Task<SystemUserAccount> GetUserAccountByUserNameAsync(string userName)
        {
            return await _context.SystemUserAccounts.FirstOrDefaultAsync(u => u.UserName == userName && u.IsActive == true);
        }

    }
}
