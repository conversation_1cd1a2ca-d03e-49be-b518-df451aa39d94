using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using DrugPrevention.Services.TuyenTM;

namespace DrugPrevention.BlazorApp.TuyenTM.Controllers
{
    public class AccountController : Controller
    {
        private readonly IServiceProviders _serviceProviders;

        public AccountController(IServiceProviders serviceProviders)
        {
            _serviceProviders = serviceProviders;
        }

        [HttpGet]
        public async Task<IActionResult> ProcessLogin(string username, string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    TempData["ErrorMessage"] = "Please enter both username and password";
                    return Redirect("/Account/Login");
                }

                var userAccount = await _serviceProviders.System_UserAccountService.GetUserAccount(username, password);

                if (userAccount != null && userAccount.UserAccountID > 0 && userAccount.IsActive && !string.IsNullOrEmpty(userAccount.UserName))
                {
                    // Create claims exactly like ASM1
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.Name, username),
                        new Claim(ClaimTypes.Role, userAccount.RoleId.ToString()),
                    };

                    var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                    await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(identity));

                    HttpContext.Response.Cookies.Append("UserName", userAccount.UserName);

                    // Redirect to home page like ASM1
                    return Redirect("/");
                }
                else
                {
                    TempData["ErrorMessage"] = "Login fail, please check your account";
                    // Sign out any existing authentication like ASM1
                    await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                    return Redirect("/Account/Login");
                }
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = $"Login error: {ex.Message}";
                return Redirect("/Account/Login");
            }
        }

        [HttpGet]
        public async Task<IActionResult> Logout()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            HttpContext.Response.Cookies.Delete("UserName");
            return Redirect("/Account/Login");
        }
    }
}
