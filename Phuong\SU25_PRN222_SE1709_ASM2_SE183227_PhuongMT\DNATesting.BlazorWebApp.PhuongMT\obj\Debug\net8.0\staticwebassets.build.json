{"Version": 1, "Hash": "VZSTI18zZF/a0W2+G5Uhuf5Rjo22nsokC6m1FnVCE00=", "Source": "DNATesting.BlazorWebApp.PhuongMT", "BasePath": "_content/DNATesting.BlazorWebApp.PhuongMT", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "DNATesting.BlazorWebApp.PhuongMT\\wwwroot", "Source": "DNATesting.BlazorWebApp.PhuongMT", "ContentRoot": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\", "BasePath": "_content/DNATesting.BlazorWebApp.PhuongMT", "Pattern": "**"}], "Assets": [{"Identity": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DNATesting.BlazorWebApp.PhuongMT.styles.css", "SourceId": "DNATesting.BlazorWebApp.PhuongMT", "SourceType": "Computed", "ContentRoot": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/DNATesting.BlazorWebApp.PhuongMT", "RelativePath": "DNATesting.BlazorWebApp.PhuongMT#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "1wa9ca0zix", "Integrity": "YVMthB5nUwQMbXagadE82prwEM1eRQQKLa0ZnVwhcQ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DNATesting.BlazorWebApp.PhuongMT.styles.css", "FileLength": 5947, "LastWriteTime": "2025-06-12T08:52:42+00:00"}, {"Identity": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DNATesting.BlazorWebApp.PhuongMT.bundle.scp.css", "SourceId": "DNATesting.BlazorWebApp.PhuongMT", "SourceType": "Computed", "ContentRoot": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/DNATesting.BlazorWebApp.PhuongMT", "RelativePath": "DNATesting.BlazorWebApp.PhuongMT#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "1wa9ca0zix", "Integrity": "YVMthB5nUwQMbXagadE82prwEM1eRQQKLa0ZnVwhcQ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DNATesting.BlazorWebApp.PhuongMT.bundle.scp.css", "FileLength": 5947, "LastWriteTime": "2025-06-12T08:52:42+00:00"}, {"Identity": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\app.css", "SourceId": "DNATesting.BlazorWebApp.PhuongMT", "SourceType": "Discovered", "ContentRoot": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\", "BasePath": "_content/DNATesting.BlazorWebApp.PhuongMT", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "da95v2qkru", "Integrity": "u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 2591, "LastWriteTime": "2025-06-12T08:52:21+00:00"}, {"Identity": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\bootstrap\\bootstrap.min.css", "SourceId": "DNATesting.BlazorWebApp.PhuongMT", "SourceType": "Discovered", "ContentRoot": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\", "BasePath": "_content/DNATesting.BlazorWebApp.PhuongMT", "RelativePath": "bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-06-12T08:52:21+00:00"}, {"Identity": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\bootstrap\\bootstrap.min.css.map", "SourceId": "DNATesting.BlazorWebApp.PhuongMT", "SourceType": "Discovered", "ContentRoot": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\", "BasePath": "_content/DNATesting.BlazorWebApp.PhuongMT", "RelativePath": "bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-06-12T08:52:21+00:00"}, {"Identity": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\favicon.png", "SourceId": "DNATesting.BlazorWebApp.PhuongMT", "SourceType": "Discovered", "ContentRoot": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\", "BasePath": "_content/DNATesting.BlazorWebApp.PhuongMT", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-12T08:52:21+00:00"}], "Endpoints": [{"Route": "app.css", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "app.da95v2qkru.css", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "da95v2qkru"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "DNATesting.BlazorWebApp.PhuongMT.1wa9ca0zix.bundle.scp.css", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DNATesting.BlazorWebApp.PhuongMT.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5947"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YVMthB5nUwQMbXagadE82prwEM1eRQQKLa0ZnVwhcQ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wa9ca0zix"}, {"Name": "label", "Value": "DNATesting.BlazorWebApp.PhuongMT.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-YVMthB5nUwQMbXagadE82prwEM1eRQQKLa0ZnVwhcQ4="}]}, {"Route": "DNATesting.BlazorWebApp.PhuongMT.1wa9ca0zix.styles.css", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DNATesting.BlazorWebApp.PhuongMT.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5947"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YVMthB5nUwQMbXagadE82prwEM1eRQQKLa0ZnVwhcQ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1wa9ca0zix"}, {"Name": "label", "Value": "DNATesting.BlazorWebApp.PhuongMT.styles.css"}, {"Name": "integrity", "Value": "sha256-YVMthB5nUwQMbXagadE82prwEM1eRQQKLa0ZnVwhcQ4="}]}, {"Route": "DNATesting.BlazorWebApp.PhuongMT.bundle.scp.css", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DNATesting.BlazorWebApp.PhuongMT.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5947"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YVMthB5nUwQMbXagadE82prwEM1eRQQKLa0ZnVwhcQ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YVMthB5nUwQMbXagadE82prwEM1eRQQKLa0ZnVwhcQ4="}]}, {"Route": "DNATesting.BlazorWebApp.PhuongMT.styles.css", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DNATesting.BlazorWebApp.PhuongMT.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5947"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"YVMthB5nUwQMbXagadE82prwEM1eRQQKLa0ZnVwhcQ4=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YVMthB5nUwQMbXagadE82prwEM1eRQQKLa0ZnVwhcQ4="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "label", "Value": "favicon.png"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "favicon.png", "AssetFile": "D:\\PRN222\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}]}