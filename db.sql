﻿ CREATE DATABASE SU25_PRN222_SE1709_G2_DrugPreventionSystem;
 GO

USE SU25_PRN222_SE1709_G2_DrugPreventionSystem;
GO

-- Organizations Table (Table chính)
CREATE TABLE OrganizationsTuyenTM (
    OrganizationTuyenTMID INT IDENTITY(1,1) PRIMARY KEY,
    OrganizationName NVARCHAR(100) NOT NULL,
    Type NVARCHAR(50) NOT NULL CHECK (Type IN ('School', 'Hospital', 'Rehab Center', 'NGO', 'Government')),
    Address NVARCHAR(255),
    ContactEmail NVARCHAR(100),
    ContactPhone NVARCHAR(20),
    Website NVARCHAR(255),
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    IsPartner BIT DEFAULT 0,
    IsActive BIT DEFAULT 1
);

-- OrganizationPrograms Table (Table phụ)
CREATE TABLE OrganizationProgramsTuyenTM (
    OrganizationProgramTuyenTMID INT IDENTITY(1,1) PRIMARY KEY,
    OrganizationID INT NOT NULL REFERENCES OrganizationsTuyenTM(OrganizationTuyenTMID),
    ProgramToanNSID INT NOT NULL REFERENCES CommunityProgramsToanNS(ProgramToanNSID),
    JoinedDate DATETIME NOT NULL DEFAULT GETDATE(),
    ContributionDescription NTEXT,
    IsSponsor BIT DEFAULT 0,
    IsOrganizer BIT DEFAULT 0
);

INSERT INTO OrganizationsTuyenTM 
(OrganizationName, Type, Address, ContactEmail, ContactPhone, Website, IsPartner, IsActive)
VALUES
(N'Trung tâm Tư vấn Sức khỏe Thanh niên', 'Rehab Center', N'123 Đường Lê Lợi, TP. Hồ Chí Minh', '<EMAIL>', '0909123456', 'http://tamsuckhoe.vn', 1, 1),
(N'Trường THPT Nguyễn Du', 'School', N'45 Nguyễn Du, Hà Nội', '<EMAIL>', '02437370001', 'http://nguyendu.edu.vn', 0, 1),
(N'Bệnh viện Tâm thần Trung ương', 'Hospital', N'200 Phạm Văn Đồng, Hà Nội', '<EMAIL>', '02438220033', 'http://tamtthan.vn', 1, 1),
(N'Tổ chức Phi Chính phủ Phòng Chống Ma Túy (PCM)', 'NGO', N'85 Trần Hưng Đạo, Đà Nẵng', '<EMAIL>', '0912345678', 'http://pcm.org.vn', 1, 1),
(N'Sở Y tế TP.HCM', 'Government', N'59 Nguyễn Thị Minh Khai, TP. Hồ Chí Minh', '<EMAIL>', '02839308130', 'http://syt.tphcm.gov.vn', 1, 1);

-- Giả sử ProgramToanNSID từ 1 đến 5 đã tồn tại
INSERT INTO OrganizationProgramsTuyenTM 
(OrganizationID, ProgramToanNSID, JoinedDate, ContributionDescription, IsSponsor, IsOrganizer)
VALUES
(1, 1, GETDATE(), N'Hỗ trợ tư vấn tâm lý và tổ chức workshop.', 0, 1),
(2, 1, GETDATE(), N'Tham gia với vai trò tổ chức truyền thông cho học sinh.', 0, 0),
(3, 2, GETDATE(), N'Cung cấp đội ngũ bác sĩ và chuyên gia tâm lý.', 1, 0),
(4, 3, GETDATE(), N'Tài trợ toàn bộ chi phí tổ chức.', 1, 1),
(5, 4, GETDATE(), N'Chỉ đạo chuyên môn và theo dõi báo cáo.', 0, 1),
(1, 5, GETDATE(), N'Cung cấp địa điểm tổ chức và tư vấn viên.', 0, 0);


-- Users Table
CREATE TABLE UsersTuyenTM (
    UserTuyenTMID INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) NOT NULL UNIQUE,
    Password NVARCHAR(255) NOT NULL,
    Email NVARCHAR(100) NOT NULL UNIQUE,
    FirstName NVARCHAR(50),
    LastName NVARCHAR(50),
    Role NVARCHAR(20) NOT NULL CHECK (Role IN ('Guest', 'Member', 'Staff', 'Consultant', 'Manager', 'Admin')),
    RegistrationDate DATETIME NOT NULL DEFAULT GETDATE(),
    IsActive BIT NOT NULL DEFAULT 1,
    PhoneNumber NVARCHAR(20)
);

-- Courses Table
CREATE TABLE CoursesQuangTNV (
    CourseQuangTNVID INT IDENTITY(1,1) PRIMARY KEY,
    Title NVARCHAR(100) NOT NULL,
    AgeGroup NVARCHAR(50) NOT NULL,
    Category NVARCHAR(50) NOT NULL,
    DurationInMinutes INT NOT NULL,
    InstructorName NVARCHAR(100),
    ContentURL NVARCHAR(255),
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    IsActive BIT NOT NULL DEFAULT 1,
    Rating DECIMAL(3, 2) DEFAULT 0
);

-- Surveys Table
CREATE TABLE SurveysNamND (
    SurveyNamNDID INT IDENTITY(1,1) PRIMARY KEY,
    SurveyName NVARCHAR(100) NOT NULL,
    SurveyType NVARCHAR(50) NOT NULL,
    TotalQuestions INT NOT NULL,
    MaxRiskScore INT,
    TargetAudience NVARCHAR(50),
    IsActive BIT NOT NULL DEFAULT 1,
    IsAnonymous BIT DEFAULT 0,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
    Creator INT 
);

-- Consultants Table
CREATE TABLE ConsultantsTrongLH (
    ConsultantTrongLHID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT NOT NULL REFERENCES UsersTuyenTM(UserTuyenTMID),
    Specialization NVARCHAR(100) NOT NULL,
    Qualification NVARCHAR(255),
    Certification NVARCHAR(255),
    YearsOfExperience INT,
    AverageRating DECIMAL(3, 2) DEFAULT 0,
    TotalConsultations INT DEFAULT 0,
    IsAvailable BIT DEFAULT 1,
    ProfileVerified BIT DEFAULT 0
);

-- Appointments Table
CREATE TABLE AppointmentsNganVHH (
    AppointmentNganVHHID INT IDENTITY(1,1) PRIMARY KEY,
    AppointmentDateTime DATETIME NOT NULL,
    Duration INT NOT NULL,
    Status NVARCHAR(20) NOT NULL CHECK (Status IN ('Scheduled', 'Completed', 'Cancelled', 'No-show')),
    ConsultantID INT NOT NULL REFERENCES ConsultantsTrongLH(ConsultantTrongLHID),
    MeetingLink NVARCHAR(255),
    FeedbackRating INT CHECK (FeedbackRating BETWEEN 1 AND 5),
    FeedbackComments NTEXT,
    IsCancelled BIT DEFAULT 0,
    CreatedDate DATETIME NOT NULL DEFAULT GETDATE()
);

-- CommunityPrograms Table
CREATE TABLE CommunityProgramsToanNS (
    ProgramToanNSID INT IDENTITY(1,1) PRIMARY KEY,
    ProgramName NVARCHAR(100) NOT NULL,
    StartDate DATETIME NOT NULL,
    EndDate DATETIME NOT NULL,
    Location NVARCHAR(255),
    MaxParticipants INT,
    CurrentParticipants INT DEFAULT 0,
    Status NVARCHAR(20) NOT NULL CHECK (Status IN ('Planning', 'Active', 'Completed', 'Cancelled')),
    OrganizerId INT REFERENCES UsersTuyenTM(UserTuyenTMID),
    IsActive BIT DEFAULT 1
);

-- UserCourses Table
CREATE TABLE UserCoursesTuyenTM (
    UserCourseTuyenTMID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT NOT NULL REFERENCES UsersTuyenTM(UserTuyenTMID),
    CourseID INT NOT NULL REFERENCES CoursesQuangTNV(CourseQuangTNVID),
    EnrollmentDate DATETIME NOT NULL DEFAULT GETDATE(),
    CompletionDate DATETIME,
    Progress DECIMAL(5, 2) DEFAULT 0,
    Score INT,
    CertificateIssued BIT DEFAULT 0,
    Rating INT CHECK (Rating BETWEEN 1 AND 5),
    LastAccessDate DATETIME
);

-- UserSurveys Table
CREATE TABLE UserSurveysNamND (
    UserSurveyNamNDID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT REFERENCES UsersTuyenTM(UserTuyenTMID),
    SurveyID INT NOT NULL REFERENCES SurveysNamND(SurveyNamNDID),
    SubmissionDate DATETIME NOT NULL DEFAULT GETDATE(),
    RiskScore INT,
    Recommendations NTEXT,
    CompletionTime INT,
    IsAnonymous BIT DEFAULT 0,
    ResultSummary NTEXT
);

-- UserAppointments Table
CREATE TABLE UserAppointmentsNganVHH  (
    UserAppointmentNganVHHID INT IDENTITY(1,1) PRIMARY KEY,
    UserID INT NOT NULL REFERENCES UsersTuyenTM(UserTuyenTMID),
    AppointmentID INT NOT NULL REFERENCES AppointmentsNganVHH(AppointmentNganVHHID),
    BookingDate DATETIME NOT NULL DEFAULT GETDATE(),
    PrimaryReason NVARCHAR(255),
    FollowUpRequired BIT DEFAULT 0,
    PreAppointmentSurveyCompleted BIT DEFAULT 0,
    PostAppointmentFeedbackProvided BIT DEFAULT 0,
    AdditionalNotes NTEXT,
    FollowUpNotes NTEXT
);

-- ProgramParticipants Table
CREATE TABLE ProgramParticipantsToanNS (
    ParticipantToanNSID INT IDENTITY(1,1) PRIMARY KEY,
    ProgramToanNSID INT NOT NULL REFERENCES CommunityProgramsToanNS(ProgramToanNSID),
    UserID INT NOT NULL REFERENCES UsersTuyenTM(UserTuyenTMID),
    RegistrationDate DATETIME NOT NULL DEFAULT GETDATE(),
    AttendanceStatus NVARCHAR(20) CHECK (AttendanceStatus IN ('Registered', 'Attended', 'Absent', 'Cancelled')),
    FeedbackProvided BIT DEFAULT 0,
    FeedbackRating INT CHECK (FeedbackRating BETWEEN 1 AND 5),
    CertificateIssued BIT DEFAULT 0,
    ParticipantRole NVARCHAR(50),
    FeedbackComments NTEXT
);

-- SurveyQuestions Table
CREATE TABLE SurveyQuestionsQuangTNV (
    QuestionQuangTNVID INT IDENTITY(1,1) PRIMARY KEY,
    SurveyID INT NOT NULL REFERENCES SurveysNamND(SurveyNamNDID),
    QuestionText NTEXT NOT NULL,
    QuestionType NVARCHAR(50) NOT NULL CHECK (QuestionType IN ('MultipleChoice', 'SingleChoice', 'Likert', 'OpenEnded', 'YesNo')),
    Options NTEXT,
    IsRequired BIT DEFAULT 1,
    QuestionOrder INT NOT NULL,
    RiskWeight INT DEFAULT 1,
    HelpText NTEXT,
    DependsOnQuestionID INT,
    DependsOnAnswer NVARCHAR(255),
    ImageURL NVARCHAR(255)
);

-- ConsultantSchedule Table
CREATE TABLE ConsultantScheduleTrongLH (
    ScheduleTrongLHID INT IDENTITY(1,1) PRIMARY KEY,
    ConsultantID INT NOT NULL REFERENCES ConsultantsTrongLH(ConsultantTrongLHID),
    DayOfWeek INT NOT NULL CHECK (DayOfWeek BETWEEN 1 AND 7),
    StartTime TIME NOT NULL,
    EndTime TIME NOT NULL,
    IsAvailable BIT DEFAULT 1,
    RecurringPattern NVARCHAR(50) DEFAULT 'Weekly',
    EffectiveFrom DATE NOT NULL DEFAULT GETDATE(),
    EffectiveTo DATE,
    MaxAppointmentsPerSlot INT DEFAULT 1,
    BufferMinutesBetweenMeetings INT DEFAULT 15,
    Notes NTEXT,
    CONSTRAINT CK_Times CHECK (StartTime < EndTime),
    CONSTRAINT UC_ConsultantSchedule UNIQUE (ConsultantID, DayOfWeek, StartTime, EndTime, EffectiveFrom)
);

-- Indexes for performance
CREATE INDEX IDX_UsersTuyenTM_Role ON UsersTuyenTM(Role);
CREATE INDEX IDX_UsersTuyenTM_IsActive ON UsersTuyenTM(IsActive);
CREATE INDEX IDX_CoursesQuangTNV_AgeGroup ON CoursesQuangTNV(AgeGroup);
CREATE INDEX IDX_CoursesQuangTNV_Category ON CoursesQuangTNV(Category);
CREATE INDEX IDX_CoursesQuangTNV_IsActive ON CoursesQuangTNV(IsActive);
CREATE INDEX IDX_SurveysNamND_IsActive ON SurveysNamND(IsActive);
CREATE INDEX IDX_AppointmentsNganVHH_Status ON AppointmentsNganVHH(Status);
CREATE INDEX IDX_AppointmentsNganVHH_DateTime ON AppointmentsNganVHH(AppointmentDateTime);
CREATE INDEX IDX_CommunityProgramsToanNS_Status ON CommunityProgramsToanNS(Status);
CREATE INDEX IDX_ConsultantsTrongLH_Specialization ON ConsultantsTrongLH(Specialization); 
CREATE INDEX IDX_ConsultantsTrongLH_IsAvailable ON ConsultantsTrongLH(IsAvailable);
