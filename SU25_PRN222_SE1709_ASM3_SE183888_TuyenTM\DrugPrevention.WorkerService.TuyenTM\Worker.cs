using DrugPrevention.Services.TuyenTM;
using DrugPrevention.WorkerService.TuyenTM.Models;

namespace DrugPrevention.WorkerService.TuyenTM
{
    public class Worker : BackgroundService
    {
        private readonly ILogger<Worker> _logger;
        private List<OrganizationProgramsTuyenTM> items;

        private readonly IOrganizationProgramsTuyenTMService _organizationProgramsTuyenTMService;
        private readonly IServiceProviders _serviceProviders;

        public Worker(IServiceProviders serviceProviders, IOrganizationProgramsTuyenTMService organizationProgramsTuyenTMService, ILogger<Worker> logger)
        {
            _serviceProviders = serviceProviders;
            _organizationProgramsTuyenTMService = organizationProgramsTuyenTMService;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                if (_logger.IsEnabled(LogLevel.Information))
                {
                    _logger.LogInformation("Worker running at: {time}", DateTimeOffset.Now);
                    ////TuyenTM || Do Action
                    //this.WriteLogDataFromDB();
                    this.WriteLogOrganizationProgramData();
                }
                await Task.Delay(1000, stoppingToken);
            }
        }

        private async void WriteLogDataFromDB()
        {
            
            try
            {
                var content = string.Empty;
                var organizationPrograms = await _serviceProviders.OrganizationProgramsTuyenTMService.GetAllAsync();
                if (organizationPrograms != null && organizationPrograms.Count > 0)
                {
                    items = organizationPrograms.Select(x => new OrganizationProgramsTuyenTM
                    {
                        OrganizationProgramTuyenTMID = x.OrganizationProgramTuyenTMID,
                        ContributionDescription = x.ContributionDescription,
                        Feedback = x.Feedback,
                        FundingAmount = x.FundingAmount,
                        IsOrganizer = x.IsOrganizer,
                        IsSponsor = x.IsSponsor,
                        JoinedDate = x.JoinedDate,
                        LastUpdated = x.LastUpdated,
                        ProgramRole = x.ProgramRole
                    }).ToList();
                    content = Utilities.ConvertObjecttoJSONString(items);
                    Utilities.WriteLoggerFile(content);
                }
            }
            catch (Exception ex)
            {

            }
        }

        private async void WriteLogOrganizationProgramData()
        {
            try
            {
                var content = string.Empty;
                var organizationPrograms = await _serviceProviders.OrganizationProgramsTuyenTMService.GetAllAsync();
                items = organizationPrograms.Select(x => new OrganizationProgramsTuyenTM
                {
                    OrganizationProgramTuyenTMID = x.OrganizationProgramTuyenTMID,
                    ContributionDescription = x.ContributionDescription,
                    Feedback = x.Feedback,
                    FundingAmount = x.FundingAmount,
                    IsOrganizer = x.IsOrganizer,
                    IsSponsor = x.IsSponsor,
                    JoinedDate = x.JoinedDate,
                    LastUpdated = x.LastUpdated,
                    ProgramRole = x.ProgramRole
                }).ToList();
                if (organizationPrograms != null && organizationPrograms.Count > 0)
                {
                    content = Utilities.ConvertObjecttoJSONString(items);
                    Utilities.WriteLoggerFile(content);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error writing log for organization programs");
            }
        }
    }
}
