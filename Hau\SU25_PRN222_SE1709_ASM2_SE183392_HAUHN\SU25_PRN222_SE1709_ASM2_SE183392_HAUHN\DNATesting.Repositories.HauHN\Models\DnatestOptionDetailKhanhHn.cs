﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DNATesting.Repositories.HauHN.Models;

public partial class DnatestOptionDetailKhanhHn
{
    public string DnatestOptionDetailKhanhHnid { get; set; }

    public string DnatestOptionKhanhHnid { get; set; }

    public string SubOptionName { get; set; }

    public decimal? ComponentPrice { get; set; }

    public double? ProcessingTime { get; set; }

    public string Note { get; set; }

    public string SampleMethod { get; set; }

    public int? SampleCount { get; set; }

    public string SampleRole { get; set; }

    public string ResultFormat { get; set; }

    public bool? IsActive { get; set; }

    public DateTime? CreatedAt { get; set; }

    public string CreatBy { get; set; }

    public DateTime? LastUpdated { get; set; }

    public string UpdateBy { get; set; }

    public virtual DnatestOptionKhanhHn DnatestOptionKhanhHn { get; set; }
}