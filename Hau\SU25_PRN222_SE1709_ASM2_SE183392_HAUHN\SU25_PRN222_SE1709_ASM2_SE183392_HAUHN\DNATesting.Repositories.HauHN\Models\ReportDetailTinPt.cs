﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DNATesting.Repositories.HauHN.Models;

public partial class ReportDetailTinPt
{
    public string ReportDetailTinPtid { get; set; }

    public string ReportTinPtid { get; set; }

    public string OptionIdkhanhHn { get; set; }

    public int? Quantity { get; set; }

    public decimal? TotalRevenue { get; set; }

    public DateTime? GeneratedAt { get; set; }

    public DateTime? DateStart { get; set; }

    public DateTime? DateEnd { get; set; }

    public bool? HasAnomalies { get; set; }

    public string ReasonForReport { get; set; }

    public string RevenueNote { get; set; }

    public virtual DnatestOptionKhanhHn OptionIdkhanhHnNavigation { get; set; }

    public virtual ReportTinPt ReportTinPt { get; set; }
}