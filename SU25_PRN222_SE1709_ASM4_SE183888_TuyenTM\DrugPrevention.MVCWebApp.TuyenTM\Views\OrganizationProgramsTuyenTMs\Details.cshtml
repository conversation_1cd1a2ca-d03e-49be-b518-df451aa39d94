﻿@model DrugPrevention.Repositories.TuyenTM.Models.OrganizationProgramsTuyenTM

@{
    ViewData["Title"] = "Program Details";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Organization Program Details
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-calendar me-1"></i>Joined Date
                                </label>
                                <div class="p-2 bg-light rounded">
                                    <span class="badge bg-info">@Model.JoinedDate.ToString("MMMM dd, yyyy")</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-clock me-1"></i>Last Updated
                                </label>
                                <div class="p-2 bg-light rounded">
                                    @if(Model.LastUpdated.HasValue) {
                                        <span class="badge bg-secondary">@Model.LastUpdated.Value.ToString("MMMM dd, yyyy HH:mm")</span>
                                    } else {
                                        <span class="text-muted">Not updated</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-building me-1"></i>Organization
                                </label>
                                <div class="p-2 bg-light rounded">
                                    <span class="badge bg-warning text-dark">@Html.DisplayFor(model => model.Organization.OrganizationName)</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-project-diagram me-1"></i>Program
                                </label>
                                <div class="p-2 bg-light rounded">
                                    <span class="badge bg-info">@Html.DisplayFor(model => model.ProgramToanNS.ProgramName)</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">
                            <i class="fas fa-comment me-1"></i>Contribution Description
                        </label>
                        <div class="p-3 bg-light rounded">
                            @Html.DisplayFor(model => model.ContributionDescription)
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-dollar-sign me-1"></i>Is Sponsor
                                </label>
                                <div class="p-2 bg-light rounded text-center">
                                    @if(Model.IsSponsor == true) {
                                        <span class="badge bg-success"><i class="fas fa-check"></i> Yes</span>
                                    } else if(Model.IsSponsor == false) {
                                        <span class="badge bg-secondary"><i class="fas fa-times"></i> No</span>
                                    } else {
                                        <span class="badge bg-light text-dark">N/A</span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-users me-1"></i>Is Organizer
                                </label>
                                <div class="p-2 bg-light rounded text-center">
                                    @if(Model.IsOrganizer == true) {
                                        <span class="badge bg-success"><i class="fas fa-check"></i> Yes</span>
                                    } else if(Model.IsOrganizer == false) {
                                        <span class="badge bg-secondary"><i class="fas fa-times"></i> No</span>
                                    } else {
                                        <span class="badge bg-light text-dark">N/A</span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-user-tag me-1"></i>Program Role
                                </label>
                                <div class="p-2 bg-light rounded text-center">
                                    <span class="badge bg-primary">@Html.DisplayFor(model => model.ProgramRole)</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-money-bill me-1"></i>Funding Amount
                                </label>
                                <div class="p-2 bg-light rounded text-center">
                                    @if(Model.FundingAmount.HasValue) {
                                        <span class="text-success fw-bold">$@Model.FundingAmount.Value.ToString("N2")</span>
                                    } else {
                                        <span class="text-muted">No funding</span>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label text-muted">
                            <i class="fas fa-star me-1"></i>Feedback
                        </label>
                        <div class="p-3 bg-light rounded">
                            @if(!string.IsNullOrEmpty(Model.Feedback)) {
                                @Html.DisplayFor(model => model.Feedback)
                            } else {
                                <span class="text-muted fst-italic">No feedback provided</span>
                            }
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>Back to List
                        </a>
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model?.OrganizationProgramTuyenTMID" class="btn btn-warning me-2">
                                <i class="fas fa-edit me-1"></i>Edit
                            </a>
                            <a asp-action="Delete" asp-route-id="@Model?.OrganizationProgramTuyenTMID" class="btn btn-outline-danger">
                                <i class="fas fa-trash me-1"></i>Delete
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
