﻿@page
@model DrugPrevention.RazorWebApp.TuyenTM.Pages.OrganizationProgramTuyenTM.CreateModel

<h4>OrganizationProgramsTuyenTM</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form method="post">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="OrganizationProgramsTuyenTM.OrganizationID" class="control-label"></label>
                <select asp-for="OrganizationProgramsTuyenTM.OrganizationID" class ="form-control" asp-items="ViewBag.OrganizationID"></select>
            </div>
            <div class="form-group">
                <label asp-for="OrganizationProgramsTuyenTM.ProgramToanNSID" class="control-label"></label>
                <select asp-for="OrganizationProgramsTuyenTM.ProgramToanNSID" class ="form-control" asp-items="ViewBag.ProgramToanNSID"></select>
            </div>
            <div class="form-group form-check">
                <input type="checkbox" class="form-check-input" id="IsSponsor" checked />
                <label class="form-check-label" for="IsSponsor">Is Sponsor</label>
            </div>
            <div class="form-group form-check">
                <input type="checkbox" class="form-check-input" id="IsOrganizer" checked />
                <label class="form-check-label" for="IsOrganizer">Is Organizer</label>
            </div>

            <div class="form-group">
                <label asp-for="OrganizationProgramsTuyenTM.ContributionDescription" class="control-label"></label>
                <input asp-for="OrganizationProgramsTuyenTM.ContributionDescription" class="form-control" />
                <span asp-validation-for="OrganizationProgramsTuyenTM.ContributionDescription" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="OrganizationProgramsTuyenTM.ProgramRole" class="control-label"></label>
                <input asp-for="OrganizationProgramsTuyenTM.ProgramRole" class="form-control" />
                <span asp-validation-for="OrganizationProgramsTuyenTM.ProgramRole" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="OrganizationProgramsTuyenTM.FundingAmount" class="control-label"></label>
                <input asp-for="OrganizationProgramsTuyenTM.FundingAmount" class="form-control" />
                <span asp-validation-for="OrganizationProgramsTuyenTM.FundingAmount" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="OrganizationProgramsTuyenTM.Feedback" class="control-label"></label>
                <input asp-for="OrganizationProgramsTuyenTM.Feedback" class="form-control" />
                <span asp-validation-for="OrganizationProgramsTuyenTM.Feedback" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Create" class="btn btn-primary" />
            </div>
            <div>
                <input type="button" id="btnHubCreate" value="Create by SignalRHub" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-page="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
<script src="~/js/signalr/dist/browser/signalr.js"></script>
<script>
    "use strict";

    var connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

    connection.start().then(function () {
        console.log("Connected to the SignalR Hub");
    }).catch(function (err) {
        return console.error(err.toString());
    });

    document.getElementById("btnHubCreate").addEventListener("click", function (event) {

    var dataObj = JSON.stringify({

            //int tự tăng thì để null
            // UserTuyenTMID: document.getElementById("UsersTuyenTM_UserTuyenTMID").value,
            JoinedDate: new Date().toISOString(),
            LastUpdated: new Date().toISOString(),
            IsSponsor: document.getElementById("IsSponsor").checked,
            IsOrganizer: document.getElementById("IsOrganizer").checked,
            ContributionDescription: document.getElementById("OrganizationProgramsTuyenTM_ContributionDescription").value,
            ProgramRole: document.getElementById("OrganizationProgramsTuyenTM_ProgramRole").value,
			FundingAmount: document.getElementById("OrganizationProgramsTuyenTM_FundingAmount").value,
			Feedback: document.getElementById("OrganizationProgramsTuyenTM_Feedback").value,
			OrganizationID: document.getElementById("OrganizationProgramsTuyenTM_OrganizationID").value,
            ProgramToanNSID: document.getElementById("OrganizationProgramsTuyenTM_ProgramToanNSID").value
			});     

        console.log(dataObj);

        connection.invoke("HubCreate_OrganizationProgramTuyenTM", dataObj).catch(function (err) {
            return console.error(err.toString());
        });
        event.preventDefault();
          });
</script>   