﻿using DNATesting.Repositories.PhuongMT.Basic;
using DNATesting.Repositories.PhuongMT.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Repositories.PhuongMT
{
    public class SamplePhuongMTRepository : GenericRepository<SamplePhuongMt>
    {
        public SamplePhuongMTRepository() { }

        public SamplePhuongMTRepository(SE18_PRN222_SE1709_G3_DNA_TestingContext context) => _context = context;

        public async Task<List<SamplePhuongMt>> GetAllAsync()
        {
            var items = await _context.SamplePhuongMts.Include(c => c.AppointmentHanNhm).ToListAsync();
            return items ?? new List<SamplePhuongMt>();
        }

        public async Task<SamplePhuongMt> GetByIDAsync(Guid code)
        {
            var items = await _context.SamplePhuongMts.Include(d => d.AppointmentHanNhm).FirstOrDefaultAsync(d => d.SamplePhuongMtid == code.ToString());
            return items ?? new SamplePhuongMt();
        }

        public async Task<List<SamplePhuongMt>> SearchAsync(string code, decimal temperate, string source)
        {
            var items = await _context.SamplePhuongMts.Include(d =>
            d.AppointmentHanNhm).Where
            (c => (c.SamplePhuongMtid.Contains(code) || string.IsNullOrEmpty(code)
            && (c.StorageTemperature == temperate || temperate == null || temperate == 0)
            && (c.AppointmentHanNhm.ApplicationSource.Contains(source)) || string.IsNullOrEmpty(source))
            ).ToListAsync();
            return items ?? new List<SamplePhuongMt>();
        }

        public async Task<bool> SamplePhuongMTExistsAsync(String id)
        {
            return await _context.SamplePhuongMts.AnyAsync(e =>e.SamplePhuongMtid == id);
        }
    }
}
