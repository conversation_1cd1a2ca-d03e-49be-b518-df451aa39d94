﻿using DrugPrevention.WorkerService.TuyenTM.Models;
using System;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace DrugPrevention.WorkerService.TuyenTM
{
    public static class Utilities
    {
        public static string ConvertObjecttoJSONString(List<OrganizationProgramsTuyenTM> entity)
        {
            var opt = new JsonSerializerOptions
            {
                WriteIndented = true, // format dễ đọc
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase, // dùng camelCase cho JSON
                ReferenceHandler = ReferenceHandler.Preserve
            };
            string jsonString = JsonSerializer.Serialize(entity, opt);
            return jsonString;
        }

        public static void WriteLoggerFile(string content)
        {
            try
            {
                var loggerFilePath = @"D:\DataLog.txt";
                using (var file = File.Open(loggerFilePath, FileMode.Append, FileAccess.Write))
                using (var writer = new StreamWriter(file))
                {
                    writer.WriteLine($"{DateTimeOffset.Now:yyyy-MM-dd HH:mm:ss} {content}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error writing log file: {ex.Message}");
            }
        }
    }
}
