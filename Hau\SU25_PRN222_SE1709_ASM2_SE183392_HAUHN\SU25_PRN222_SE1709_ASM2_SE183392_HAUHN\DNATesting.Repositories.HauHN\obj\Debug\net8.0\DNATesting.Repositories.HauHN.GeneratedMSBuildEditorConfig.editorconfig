is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = DNATesting.Repositories.HauHN
build_property.ProjectDir = D:\FPTU\S7\PRN222\Hau\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\DNATesting.Repositories.HauHN\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 
