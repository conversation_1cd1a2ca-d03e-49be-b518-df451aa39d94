﻿using DNATesting.Repositories.PhuongMT.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.PhuongMT
{
    public interface ISamplePhuongMTService
    {
        Task<List<SamplePhuongMt>> GetAllAsync();
        Task<SamplePhuongMt> GetByIdAsync(string code);
        Task<List<SamplePhuongMt>> SearchAsync(string code, decimal amount, string bankNo);
        Task<int> CreateAsync(SamplePhuongMt sample);
        Task<int> UpdateAsync(SamplePhuongMt sample);
        Task<bool> DeleteAsync(SamplePhuongMt sample);
        Task<bool> SamplePhuongMTExistsAsync(String id);
        Task<bool> DeleteStringAsync(string sample);
    }
}
