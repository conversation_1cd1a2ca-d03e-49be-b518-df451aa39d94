﻿using DNATesting.Repositories.HauHN;
using DNATesting.Repositories.HauHN.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.HauHN
{
    public class AppointmentHanNhmService : IAppointmentHanNhmService
    {
        private readonly UnitOfWork unitOfWork;
        public AppointmentHanNhmService() => unitOfWork ??= new UnitOfWork();
        public async Task<List<AppointmentHanNhm>> GetAllAsync()
        {
            return await unitOfWork.AppointmentHanNhmRepository.GetAllAsync();
        }
    }
}
