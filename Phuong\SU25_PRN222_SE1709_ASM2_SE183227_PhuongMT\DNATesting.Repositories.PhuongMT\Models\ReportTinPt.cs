﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DNATesting.Repositories.PhuongMT.Models;

public partial class ReportTinPt
{
    public string ReportTinPtid { get; set; }

    public int? ReportType { get; set; }

    public int GeneratedBy { get; set; }

    public DateTime? GeneratedAt { get; set; }

    public int? TotalAppointments { get; set; }

    public int? TotalRevenue { get; set; }

    public string Notes { get; set; }

    public DateTime? DateStart { get; set; }

    public DateTime? DateEnd { get; set; }

    public virtual SystemUserAccount GeneratedByNavigation { get; set; }

    public virtual ICollection<ReportDetailTinPt> ReportDetailTinPts { get; set; } = new List<ReportDetailTinPt>();

    public virtual ICollection<AppointmentHanNhm> AppointmentHanNhms { get; set; } = new List<AppointmentHanNhm>();
}