﻿using DNATesting.Repositories.HauHN;
using DNATesting.Repositories.HauHN.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.HauHN
{
    public class TestResultDetailsHauHnService : ITestResultDetailsHauHnService
    {
        private readonly UnitOfWork unitOfWork;
        public TestResultDetailsHauHnService() => unitOfWork ??= new UnitOfWork();

        public async Task<List<TestResultDetailsHauHn>> GetAllAsync()
        {
            return await unitOfWork.TestResultDetailsHauHnRepository.GetAllAsync();
        }
    }
}
