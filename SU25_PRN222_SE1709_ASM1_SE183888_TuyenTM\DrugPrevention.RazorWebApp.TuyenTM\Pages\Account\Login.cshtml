﻿@page
@model DrugPrevention.RazorWebApp.TuyenTM.Pages.Account.LoginModel
@{
    Layout = null;
}

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - DrugPrevention.RazorWebApp.TuyenTM</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/zPayment.RazorWebApp.VuLNS.styles.css" asp-append-version="true" />
</head>

<body class="body">
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="container">
                <a class="navbar-brand" asp-area="" asp-page="/Index">DrugPrevention.RazorWebApp.TuyenTM - <span class="text-danger">PRN222</span></a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>                
            </div>
        </nav>
    </header>
    <div class="container">        
        <div class="row">
            <h2 class="text-center">Log-in</h2>            
            <div class="col-md-4 mx-auto">
                @if (TempData["Message"] != null)
                {
                    <div class="text-danger">@TempData["Message"]</div>
                }

                <form method="post">
                    <div class="form-group">
                        <label asp-for="UserName"></label>
                        @* <input type="email" class="form-control" asp-for="UserName" required /> *@
                        <input type="text" class="form-control" asp-for="UserName" required />
                        <span asp-validation-for="UserName" class="text-danger"></span>
                    </div>

                    <div class="form-group">
                        <label asp-for="Password"></label>
                        <input type="password" class="form-control" asp-for="Password" required />
                        <span asp-validation-for="Password" class="text-danger"></span>
                    </div>

                    <button type="submit" class="btn btn-primary">Login</button>
                </form>
            </div>
        </div>
    </div>
</body>