﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DNATesting.Repositories.PhuongMT.Models;

public partial class TestResultHauHn
{
    public string ResultHauHnid { get; set; }

    public string AppointmentHanNhmid { get; set; }

    public string TestType { get; set; }

    public string ResultValue { get; set; }

    public DateTime? ResultDate { get; set; }

    public DateTime? TestDate { get; set; }

    public int TechnicianId { get; set; }

    public bool? IsVerified { get; set; }

    public int VerifiedBy { get; set; }

    public string GeneticMutation { get; set; }

    public decimal? DiseaseRisk { get; set; }

    public decimal? RelationshipProbability { get; set; }

    public virtual AppointmentHanNhm AppointmentHanNhm { get; set; }

    public virtual SystemUserAccount Technician { get; set; }

    public virtual ICollection<TestResultDetailsHauHn> TestResultDetailsHauHns { get; set; } = new List<TestResultDetailsHauHn>();

    public virtual SystemUserAccount VerifiedByNavigation { get; set; }

    public virtual ICollection<TestParticipantsHauHn> ParticipantHauHns { get; set; } = new List<TestParticipantsHauHn>();

    public virtual ICollection<SamplePhuongMt> SamplePhuongMts { get; set; } = new List<SamplePhuongMt>();
}