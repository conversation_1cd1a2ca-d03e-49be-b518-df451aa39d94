﻿@page
@model DrugPrevention.RazorWebApp.TuyenTM.Pages.OrganizationProgramTuyenTM.EditModel

<div class="container mt-4">
    <h2 class="mb-4">Edit Organization Program</h2>
    <hr />
    <form method="post" class="needs-validation" novalidate>
        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
        <input id="OrganizationProgramsTuyenTM_OrganizationProgramTuyenTMID" type="hidden" asp-for="OrganizationProgramsTuyenTM.OrganizationProgramTuyenTMID" />

        <div class="row g-3">
            <div class="col-md-6">
                <label asp-for="OrganizationProgramsTuyenTM.OrganizationID" class="form-label"></label>
                <select id="OrganizationProgramsTuyenTM_OrganizationID" asp-for="OrganizationProgramsTuyenTM.OrganizationID" class="form-select" asp-items="ViewBag.OrganizationID"></select>
                <span asp-validation-for="OrganizationProgramsTuyenTM.OrganizationID" class="text-danger"></span>
            </div>

            <div class="col-md-6">
                <label asp-for="OrganizationProgramsTuyenTM.ProgramToanNSID" class="form-label"></label>
                <select id="OrganizationProgramsTuyenTM_ProgramToanNSID" asp-for="OrganizationProgramsTuyenTM.ProgramToanNSID" class="form-select" asp-items="ViewBag.ProgramToanNSID"></select>
                <span asp-validation-for="OrganizationProgramsTuyenTM.ProgramToanNSID" class="text-danger"></span>
            </div>

            @* <div class="col-md-6"> *@
            @*     <label asp-for="OrganizationProgramsTuyenTM.JoinedDate" class="form-label"></label> *@
            @*     <input asp-for="OrganizationProgramsTuyenTM.JoinedDate" type="datetime-local" class="form-control" /> *@
            @*     <span asp-validation-for="OrganizationProgramsTuyenTM.JoinedDate" class="text-danger"></span> *@
            @* </div> *@

            @* <div class="col-md-6"> *@
            @*     <label asp-for="OrganizationProgramsTuyenTM.LastUpdated" class="form-label"></label> *@
            @*     <input asp-for="OrganizationProgramsTuyenTM.LastUpdated" type="datetime-local" class="form-control" /> *@
            @*     <span asp-validation-for="OrganizationProgramsTuyenTM.LastUpdated" class="text-danger"></span> *@
            @* </div> *@

            <div class="col-md-6">
                <label asp-for="OrganizationProgramsTuyenTM.JoinedDate" class="form-label"></label>
                <input id="OrganizationProgramsTuyenTM_JoinedDate" type="text"
                       class="form-control"
                       value="@Model.OrganizationProgramsTuyenTM.JoinedDate.ToString("HH:mm dd/MM/yyyy")"
                       readonly />
            </div>

            <div class="col-md-6">
                <label asp-for="OrganizationProgramsTuyenTM.LastUpdated" class="form-label"></label>
                <input type="text"
                       class="form-control"
                       value="@Model.OrganizationProgramsTuyenTM.LastUpdated?.ToString("HH:mm dd/MM/yyyy")"
                       readonly />
            </div>

            <div class="col-md-12">
                <label asp-for="OrganizationProgramsTuyenTM.ContributionDescription" class="form-label"></label>
                <input asp-for="OrganizationProgramsTuyenTM.ContributionDescription" class="form-control" />
                <span asp-validation-for="OrganizationProgramsTuyenTM.ContributionDescription" class="text-danger"></span>
            </div>

            @* <div class="col-md-6 form-check mt-2"> *@
            @*     <input asp-for="OrganizationProgramsTuyenTM.IsSponsor" class="form-check-input" type="checkbox" /> *@
            @*     <label asp-for="OrganizationProgramsTuyenTM.IsSponsor" class="form-check-label"></label> *@
            @*     <span asp-validation-for="OrganizationProgramsTuyenTM.IsSponsor" class="text-danger"></span> *@
            @* </div> *@

            @* <div class="col-md-6 form-check mt-2"> *@
            @*     <input asp-for="OrganizationProgramsTuyenTM.IsOrganizer" class="form-check-input" type="checkbox" /> *@
            @*     <label asp-for="OrganizationProgramsTuyenTM.IsOrganizer" class="form-check-label"></label> *@
            @*     <span asp-validation-for="OrganizationProgramsTuyenTM.IsOrganizer" class="text-danger"></span> *@
            @* </div> *@

            <div class="col-md-6">
                <div class="form-check mt-2">
                    <input type="checkbox" class="form-check-input"
                           id="IsSponsor"
                           name="OrganizationProgramsTuyenTM.IsSponsor"
                           value="true"
                    @(Model.OrganizationProgramsTuyenTM.IsSponsor == true ? "checked" : "") />
                    <label for="IsSponsor" class="form-check-label">Is Sponsor</label>
                </div>
            </div>

            <div class="col-md-6">
                <div class="form-check mt-2">
                    <input type="checkbox" class="form-check-input"
                           id="IsOrganizer"
                           name="OrganizationProgramsTuyenTM.IsOrganizer"
                           value="true"
                    @(Model.OrganizationProgramsTuyenTM.IsOrganizer == true ? "checked" : "") />
                    <label for="IsOrganizer" class="form-check-label">Is Organizer</label>
                </div>
            </div>


            <div class="col-md-6">
                <label asp-for="OrganizationProgramsTuyenTM.ProgramRole" class="form-label"></label>
                <input asp-for="OrganizationProgramsTuyenTM.ProgramRole" class="form-control" />
                <span asp-validation-for="OrganizationProgramsTuyenTM.ProgramRole" class="text-danger"></span>
            </div>

            <div class="col-md-6">
                <label asp-for="OrganizationProgramsTuyenTM.FundingAmount" class="form-label"></label>
                <input asp-for="OrganizationProgramsTuyenTM.FundingAmount" type="number" class="form-control" />
                <span asp-validation-for="OrganizationProgramsTuyenTM.FundingAmount" class="text-danger"></span>
            </div>

            <div class="col-12">
                <label asp-for="OrganizationProgramsTuyenTM.Feedback" class="form-label"></label>
                <textarea asp-for="OrganizationProgramsTuyenTM.Feedback" class="form-control" rows="3"></textarea>
                <span asp-validation-for="OrganizationProgramsTuyenTM.Feedback" class="text-danger"></span>
            </div>

            <div class="col-12 mt-4 d-flex justify-content-between">
                @* <a asp-page="./Index" class="btn btn-secondary">Back to List</a> *@
                <div class="mt-3 mb-5">
                    <a asp-page="./Index" class="btn btn-secondary">Back to List</a>
                </div>
                <div class="mt-3 mb-5">
                    <input type="submit" value="Save Changes" class="btn btn-primary" />
                </div>
                <div class="mt-3 mb-5">
                    <input type="button" value="Save Changes with SignalR" class="btn btn-primary" id="btnHubUpdate" />
                </div>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
<script src="~/js/signalr/dist/browser/signalr.js"></script>
<script>
    "use strict";

    // Hàm chuyển đổi định dạng "HH:mm dd/MM/yyyy" sang ISO string
    function parseDateTimeVNFormatToISOString(dateString) {
        if (!dateString) return null;

        const parts = dateString.trim().split(" ");
        if (parts.length !== 2) return null;

        const timeParts = parts[0].split(":");
        const dateParts = parts[1].split("/");

        if (timeParts.length !== 2 || dateParts.length !== 3) return null;

        const hours = parseInt(timeParts[0], 10);
        const minutes = parseInt(timeParts[1], 10);
        const day = parseInt(dateParts[0], 10);
        const month = parseInt(dateParts[1], 10) - 1; // tháng trong JS bắt đầu từ 0
        const year = parseInt(dateParts[2], 10);

        const dateObj = new Date(year, month, day, hours, minutes);
        return dateObj.toISOString();
    }

    // Kết nối SignalR
    var connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

    connection.start().then(function () {
        console.log("Connected to the SignalR Hub");
    }).catch(function (err) {
        return console.error(err.toString());
    });

    // Bắt sự kiện click nút Save Changes with SignalR
    document.getElementById("btnHubUpdate").addEventListener("click", function (event) {
        event.preventDefault();

        const joinedDateRaw = document.getElementById("OrganizationProgramsTuyenTM_JoinedDate").value;
        const joinedDateISO = parseDateTimeVNFormatToISOString(joinedDateRaw);
        const lastUpdatedISO = new Date().toISOString();

        const dataObj = JSON.stringify({
            OrganizationProgramTuyenTMID: document.getElementById("OrganizationProgramsTuyenTM_OrganizationProgramTuyenTMID").value,
            JoinedDate: joinedDateISO,
            LastUpdated: lastUpdatedISO,
            IsSponsor: document.getElementById("IsSponsor").checked,
            IsOrganizer: document.getElementById("IsOrganizer").checked,
            ContributionDescription: document.getElementById("OrganizationProgramsTuyenTM_ContributionDescription").value,
            ProgramRole: document.getElementById("OrganizationProgramsTuyenTM_ProgramRole").value,
            FundingAmount: document.getElementById("OrganizationProgramsTuyenTM_FundingAmount").value,
            Feedback: document.getElementById("OrganizationProgramsTuyenTM_Feedback").value,
            OrganizationID: document.getElementById("OrganizationProgramsTuyenTM_OrganizationID").value,
            ProgramToanNSID: document.getElementById("OrganizationProgramsTuyenTM_ProgramToanNSID").value
        });

        console.log("Sending via SignalR:", dataObj);

        connection.invoke("HubUpdate_OrganizationProgramTuyenTM", dataObj).catch(function (err) {
            return console.error(err.toString());
        });
    });
</script>