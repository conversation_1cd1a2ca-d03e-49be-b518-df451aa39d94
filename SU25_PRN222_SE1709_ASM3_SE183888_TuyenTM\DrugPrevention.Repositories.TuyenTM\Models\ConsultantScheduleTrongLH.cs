﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DrugPrevention.Repositories.TuyenTM.Models;

public partial class ConsultantScheduleTrongLH
{
    public int ScheduleTrongLHID { get; set; }

    public int ConsultantID { get; set; }

    public int DayOfWeek { get; set; }

    public TimeOnly StartTime { get; set; }

    public TimeOnly EndTime { get; set; }

    public bool? IsAvailable { get; set; }

    public string RecurringPattern { get; set; }

    public DateOnly EffectiveFrom { get; set; }

    public DateOnly? EffectiveTo { get; set; }

    public int? MaxAppointmentsPerSlot { get; set; }

    public int? BufferMinutesBetweenMeetings { get; set; }

    public string Notes { get; set; }

    public virtual ConsultantsTrongLH Consultant { get; set; }
}