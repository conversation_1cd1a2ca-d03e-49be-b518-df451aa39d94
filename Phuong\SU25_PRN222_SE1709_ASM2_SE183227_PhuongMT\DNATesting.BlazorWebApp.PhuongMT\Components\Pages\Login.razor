﻿@page "/login"

<h3>Login</h3>

<div class="card p-4" style="max-width: 400px; margin: auto;">
    <EditForm Model="@loginModel" OnValidSubmit="HandleLogin">
        <DataAnnotationsValidator />
        <ValidationSummary />

        <div class="mb-3">
            <label for="email" class="form-label">Email</label>
            <InputText id="email" class="form-control" @bind-Value="loginModel.Email" />
            <ValidationMessage For="@(() => loginModel.Email)" />
        </div>

        <div class="mb-3">
            <label for="password" class="form-label">Password</label>
            <InputText id="password" type="password" class="form-control" @bind-Value="loginModel.Password" />
            <ValidationMessage For="@(() => loginModel.Password)" />
        </div>

        <button type="submit" class="btn btn-primary">Login</button>

        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger mt-3">@errorMessage</div>
        }
    </EditForm>
</div>


