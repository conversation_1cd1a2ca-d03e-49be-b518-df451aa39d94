﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.HauHN
{
    public interface IServiceProviders
    {

        ISystemUserAccountService systemUserAccountService { get; }
        ITestResultHauHNService testResultHauHNService { get; }
        ITestResultDetailsHauHnService testResultDetailsHauHNService { get; }
        IAppointmentHanNhmService appointmentHanNhmService { get; }
    }
    public class ServiceProviders : IServiceProviders
    {
        private ISystemUserAccountService _systemUserAccountServiceImpl;
        private ITestResultHauHNService _testResultHauHNServiceImpl;
        private ITestResultDetailsHauHnService _ItestResultDetailsHauHNService;
        private IAppointmentHanNhmService _IappointmentHanNhmService;
        public ServiceProviders() { }

        public ISystemUserAccountService systemUserAccountService
        {
            get { return _systemUserAccountServiceImpl ??= new SystemUserAccountService(); }
        }

        public ITestResultHauHNService testResultHauHNService
        {
            get { return _testResultHauHNServiceImpl ??= new TestResultHauHNService(); }
        }

        public ITestResultDetailsHauHnService testResultDetailsHauHNService
        {
            get { return _ItestResultDetailsHauHNService ??= new TestResultDetailsHauHnService(); }
        }

        public IAppointmentHanNhmService appointmentHanNhmService
        {
            get { return _IappointmentHanNhmService ??= new AppointmentHanNhmService(); }
        }

        public object? GetService(Type serviceType)
        {
            throw new NotImplementedException();
        }
    }
}
