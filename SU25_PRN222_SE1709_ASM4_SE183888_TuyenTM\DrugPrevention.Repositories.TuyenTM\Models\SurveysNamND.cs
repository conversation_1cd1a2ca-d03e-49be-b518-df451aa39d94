﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DrugPrevention.Repositories.TuyenTM.Models;

public partial class SurveysNamND
{
    public int SurveyNamNDID { get; set; }

    public string SurveyName { get; set; }

    public string SurveyType { get; set; }

    public int TotalQuestions { get; set; }

    public int? MaxRiskScore { get; set; }

    public string TargetAudience { get; set; }

    public bool IsActive { get; set; }

    public bool? IsAnonymous { get; set; }

    public DateTime CreatedDate { get; set; }

    public int? Creator { get; set; }

    public virtual ICollection<SurveyQuestionsQuangTNV> SurveyQuestionsQuangTNVs { get; set; } = new List<SurveyQuestionsQuangTNV>();

    public virtual ICollection<UserSurveysNamND> UserSurveysNamNDs { get; set; } = new List<UserSurveysNamND>();
}