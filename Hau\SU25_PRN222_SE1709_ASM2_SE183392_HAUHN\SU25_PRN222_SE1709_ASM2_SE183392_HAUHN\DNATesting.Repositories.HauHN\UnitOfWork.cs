﻿using DNATesting.Repositories.HauHN.DBContext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Repositories.HauHN
{
    public interface IUnitOfWork : IDisposable
    {
        SystemUserAccountRepository SystemUserAccountRepository { get; }
        TestResultHauHNRepository TestResultHauHnRepository { get; }
        TestResultDetailsHauHnRepository TestResultDetailsHauHnRepository { get; }
        AppointmentHanNhmRepository AppointmentHanNhmRepository { get; }
        Task<int> SaveChangesWithTransaction();
        Task<int> SaveChangesAsync();
    }
    public class UnitOfWork : IUnitOfWork
    {
        private readonly SE18_PRN222_SE1709_G3_DNA_TestingContext _context;
        private SystemUserAccountRepository _systemUserAccountRepository;
        private TestResultHauHNRepository _testResultHauHnRepository;
        private TestResultDetailsHauHnRepository _testResultDetailsHauHnRepository;
        private AppointmentHanNhmRepository _appointmentHanNhmRepository;

        public UnitOfWork() => _context ??= new SE18_PRN222_SE1709_G3_DNA_TestingContext();
        public SystemUserAccountRepository SystemUserAccountRepository
        {
            get { return _systemUserAccountRepository ??= new SystemUserAccountRepository(_context); }
        }

        public TestResultHauHNRepository TestResultHauHnRepository
        {
            get { return _testResultHauHnRepository ??= new TestResultHauHNRepository(_context); }
        }

        public TestResultDetailsHauHnRepository TestResultDetailsHauHnRepository
        {
            get { return _testResultDetailsHauHnRepository ??= new TestResultDetailsHauHnRepository(_context); }
        }

        public AppointmentHanNhmRepository AppointmentHanNhmRepository
        {
            get { return _appointmentHanNhmRepository ??= new AppointmentHanNhmRepository(_context); }
        }

        public void Dispose() => _context?.Dispose();

        public async Task<int> SaveChangesAsync()
        {
            int result = -1;

            //System.Data.IsolationLevel.Snapshot
            using (var dbContextTransaction = _context.Database.BeginTransaction())
            {
                try
                {
                    result = await _context.SaveChangesAsync();
                    dbContextTransaction.Commit();
                }
                catch (Exception)
                {
                    //Log Exception Handling message                      
                    result = -1;
                    dbContextTransaction.Rollback();
                }
            }

            return result;
        }

        public async Task<int> SaveChangesWithTransaction()
        {
            int result = -1;

            //System.Data.IsolationLevel.Snapshot
            using (var dbContextTransaction = _context.Database.BeginTransaction())
            {
                try
                {
                    result = _context.SaveChanges();
                    dbContextTransaction.Commit();
                }
                catch (Exception)
                {
                    //Log Exception Handling message                      
                    result = -1;
                    dbContextTransaction.Rollback();
                }
            }

            return result;
        }
    }
}
