﻿@page
@model DrugPrevention.RazorWebApp.TuyenTM.Pages.UsersTuyenTMs.DetailsModel

@{
    ViewData["Title"] = "User Details";
}

<div class="container mt-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h4 class="mb-0"><i class="bi bi-person-circle me-2"></i> User Details</h4>
            <a class="btn btn-sm btn-outline-light" asp-page="./Index">
                <i class="bi bi-arrow-left"></i> Back to List
            </a>
        </div>
        <div class="card-body" id="UserTuyenTMID">
            <dl class="row">

				@* <dt class="col-sm-3">Organization Program ID</dt> *@
    @*             <dd class="col-sm-10" id="TransactionCashDepositSlip_Code"> *@
    @*                 @Html.DisplayFor(model => model.TransactionCashDepositSlip.Code) *@
    @*             </dd> *@
                <dt class="col-sm-3">Username</dt>
                <dd class="col-sm-9">@Model.UsersTuyenTM.Username</dd>

                <dt class="col-sm-3">Email</dt>
                <dd class="col-sm-9">@Model.UsersTuyenTM.Email</dd>

                <dt class="col-sm-3">First Name</dt>
                <dd class="col-sm-9">@Model.UsersTuyenTM.FirstName</dd>

                <dt class="col-sm-3">Last Name</dt>
                <dd class="col-sm-9">@Model.UsersTuyenTM.LastName</dd>

                <dt class="col-sm-3">Role</dt>
                <dd class="col-sm-9">@Model.UsersTuyenTM.Role</dd>

                <dt class="col-sm-3">Phone Number</dt>
                <dd class="col-sm-9">@Model.UsersTuyenTM.PhoneNumber</dd>

                <dt class="col-sm-3">Registration Date</dt>
                <dd class="col-sm-9">@Model.UsersTuyenTM.RegistrationDate.ToString("dd-MM-yyyy")</dd>

                <dt class="col-sm-3">Status</dt>
                <dd class="col-sm-9">
                    @if (Model.UsersTuyenTM.IsActive)
                    {
                        <span class="badge bg-success">Active</span>
                    }
                    else
                    {
                        <span class="badge bg-secondary">Inactive</span>
                    }
                </dd>

                <dt class="col-sm-3">Courses</dt>
                <dd class="col-sm-9">
                    @if (Model.UsersTuyenTM.UserCoursesTuyenTMs != null && Model.UsersTuyenTM.UserCoursesTuyenTMs.Any())
                    {
                        <ul>
                            @foreach (var uc in Model.UsersTuyenTM.UserCoursesTuyenTMs)
                            {
                                <li>@uc.Course?.Title</li>
                            }
                        </ul>
                    }
                    else
                    {
                        <span class="text-muted">No courses assigned.</span>
                    }
                </dd>
            </dl>
        </div>
        <div class="card-footer text-end">
            <a class="btn btn-outline-primary me-2" asp-page="./Edit" asp-route-id="@Model.UsersTuyenTM.UserTuyenTMID">
                <i class="bi bi-pencil-square"></i> Edit
            </a>
            <a class="btn btn-outline-secondary" asp-page="./Index">
                <i class="bi bi-list"></i> Back to List
            </a>
        </div>
    </div>
</div>

<script src="~/js/signalr/dist/browser/signalr.js" ></script>
<script>
    "use strict";

    var connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

    connection.start().then(function () {
        console.log("Connected to the SignalR Hub");
    }).catch(function (err) {
        return console.error(err.toString());
    });

    connection.on("Receiver_DeleteUserTuyenTM", function (UserTuyenTMID) {
        // alert($(`#${UserTuyenTMID}`).text());
        // $('#UserTuyenTMBodyID').find(`tr[id='${UserTuyenTMID}']`).remove();
		document.getElementById("UserTuyenTMID").innerHTML = "This item had deleted";
    });

        connection.on("Receiver_UpdateUserTuyenTM", function (item) {
        if (item.userTuyenTMID == "@Model.UsersTuyenTM.UserTuyenTMID") {
            document.querySelector("dd:nth-of-type(1)").textContent = item.username;
            document.querySelector("dd:nth-of-type(2)").textContent = item.email;
            document.querySelector("dd:nth-of-type(3)").textContent = item.firstName;
            document.querySelector("dd:nth-of-type(4)").textContent = item.lastName;
            document.querySelector("dd:nth-of-type(5)").textContent = item.role;
            document.querySelector("dd:nth-of-type(6)").textContent = item.phoneNumber;
            document.querySelector("dd:nth-of-type(7)").textContent = new Date(item.registrationDate).toLocaleDateString('en-GB');

            let statusHtml = item.isActive
                ? '<span class="badge bg-success">Active</span>'
                : '<span class="badge bg-secondary">Inactive</span>';
            document.querySelector("dd:nth-of-type(8)").innerHTML = statusHtml;
        }
    });


</script>