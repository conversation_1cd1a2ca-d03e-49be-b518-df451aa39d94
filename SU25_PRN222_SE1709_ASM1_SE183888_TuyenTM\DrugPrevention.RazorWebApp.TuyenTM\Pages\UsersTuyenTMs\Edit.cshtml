﻿@page
@model DrugPrevention.RazorWebApp.TuyenTM.Pages.UsersTuyenTMs.EditModel

@{
    ViewData["Title"] = "Edit User";
    var courseList = ViewData["Courses"] as List<SelectListItem>;
}

<h2 class="mb-4">Edit User</h2>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow-sm border-0">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">User Information</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                    <input type="hidden" asp-for="UsersTuyenTM.UserTuyenTMID" />

                    <div class="form-group mb-3">
                        <label asp-for="UsersTuyenTM.Username" class="form-label"></label>
                        <input asp-for="UsersTuyenTM.Username" class="form-control" />
                        <span asp-validation-for="UsersTuyenTM.Username" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="UsersTuyenTM.Password" class="form-label"></label>
                        <input asp-for="UsersTuyenTM.Password" class="form-control" />
                        <span asp-validation-for="UsersTuyenTM.Password" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="UsersTuyenTM.Email" class="form-label"></label>
                        <input asp-for="UsersTuyenTM.Email" class="form-control" />
                        <span asp-validation-for="UsersTuyenTM.Email" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="UsersTuyenTM.FirstName" class="form-label"></label>
                        <input asp-for="UsersTuyenTM.FirstName" class="form-control" />
                        <span asp-validation-for="UsersTuyenTM.FirstName" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="UsersTuyenTM.LastName" class="form-label"></label>
                        <input asp-for="UsersTuyenTM.LastName" class="form-control" />
                        <span asp-validation-for="UsersTuyenTM.LastName" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="UsersTuyenTM.Role" class="form-label"></label>
                        <select asp-for="UsersTuyenTM.Role" class="form-control">
                            <option>Admin</option>
                            <option>Manager</option>
                            <option>Consultant</option>
                            <option>Staff</option>
                            <option>Member</option>
                            <option>Guest</option>
                        </select>
                        <span asp-validation-for="UsersTuyenTM.Role" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="UsersTuyenTM.RegistrationDate" class="form-label"></label>
                        @* <input asp-for="UsersTuyenTM.RegistrationDate"  *@
                        @*     class="form-control" *@
                        @*     value="@Model.UsersTuyenTM.RegistrationDate.ToString("HH:mm:ss dd-MM-yyyy") *@
                        @*     disabled /> *@
                        <input class="form-control"
                               value="@Model.UsersTuyenTM.RegistrationDate"
                               readonly />

                        <span asp-validation-for="UsersTuyenTM.RegistrationDate" class="text-danger"></span>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" asp-for="UsersTuyenTM.IsActive" />
                        <label class="form-check-label" asp-for="UsersTuyenTM.IsActive">
                            @Html.DisplayNameFor(model => model.UsersTuyenTM.IsActive)
                        </label>
                    </div>

                    <div class="form-group mb-3">
                        <label asp-for="UsersTuyenTM.PhoneNumber" class="form-label"></label>
                        <input asp-for="UsersTuyenTM.PhoneNumber" class="form-control" />
                        <span asp-validation-for="UsersTuyenTM.PhoneNumber" class="text-danger"></span>
                    </div>

                    <div class="form-group mb-4">
                        <label for="SelectedCourseIds" class="form-label">Courses</label>
                        <select asp-for="SelectedCourseIds"
                                asp-items="courseList"
                                class="form-control"
                                multiple
                                size="4">
                        </select>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a asp-page="./Index" class="btn btn-outline-secondary">Back to List</a>
                        <input type="submit" value="Save" class="btn btn-primary" />
						<input type="button" id="btnHubUpdate" value="Update by SignalRHub" class="btn btn-primary" />
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
}



