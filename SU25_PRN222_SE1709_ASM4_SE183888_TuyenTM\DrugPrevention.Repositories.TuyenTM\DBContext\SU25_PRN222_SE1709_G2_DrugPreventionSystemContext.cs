﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using DrugPrevention.Repositories.TuyenTM.Models;

namespace DrugPrevention.Repositories.TuyenTM.DBContext;

public partial class SU25_PRN222_SE1709_G2_DrugPreventionSystemContext : DbContext
{
    public SU25_PRN222_SE1709_G2_DrugPreventionSystemContext()
    {
    }

    public SU25_PRN222_SE1709_G2_DrugPreventionSystemContext(DbContextOptions<SU25_PRN222_SE1709_G2_DrugPreventionSystemContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AppointmentsNganVHH> AppointmentsNganVHHs { get; set; }

    public virtual DbSet<CommunityProgramsToanN> CommunityProgramsToanNs { get; set; }

    public virtual DbSet<ConsultantScheduleTrongLH> ConsultantScheduleTrongLHs { get; set; }

    public virtual DbSet<ConsultantsTrongLH> ConsultantsTrongLHs { get; set; }

    public virtual DbSet<CoursesQuangTNV> CoursesQuangTNVs { get; set; }

    public virtual DbSet<OrganizationProgramsTuyenTM> OrganizationProgramsTuyenTMs { get; set; }

    public virtual DbSet<OrganizationsTuyenTM> OrganizationsTuyenTMs { get; set; }

    public virtual DbSet<ProgramParticipantsToanN> ProgramParticipantsToanNs { get; set; }

    public virtual DbSet<SurveyQuestionsQuangTNV> SurveyQuestionsQuangTNVs { get; set; }

    public virtual DbSet<SurveysNamND> SurveysNamNDs { get; set; }

    public virtual DbSet<System_UserAccount> System_UserAccounts { get; set; }

    public virtual DbSet<UserAppointmentsNganVHH> UserAppointmentsNganVHHs { get; set; }

    public virtual DbSet<UserCoursesTuyenTM> UserCoursesTuyenTMs { get; set; }

    public virtual DbSet<UserSurveysNamND> UserSurveysNamNDs { get; set; }

    public virtual DbSet<UsersTuyenTM> UsersTuyenTMs { get; set; }

    public static string GetConnectionString(string connectionStringName)
    {
        var config = new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json")
            .Build();

        string connectionString = config.GetConnectionString(connectionStringName);
        return connectionString;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        => optionsBuilder.UseSqlServer(GetConnectionString("DefaultConnection")).UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AppointmentsNganVHH>(entity =>
        {
            entity.HasKey(e => e.AppointmentNganVHHID).HasName("PK__Appointm__826BF473A5FA9E1B");

            entity.ToTable("AppointmentsNganVHH");

            entity.HasIndex(e => e.AppointmentDateTime, "IDX_AppointmentsNganVHH_DateTime");

            entity.HasIndex(e => e.Status, "IDX_AppointmentsNganVHH_Status");

            entity.Property(e => e.AppointmentDateTime).HasColumnType("datetime");
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FeedbackComments).HasColumnType("ntext");
            entity.Property(e => e.IsCancelled).HasDefaultValue(false);
            entity.Property(e => e.MeetingLink).HasMaxLength(255);
            entity.Property(e => e.Status)
                .IsRequired()
                .HasMaxLength(20);
        });

        modelBuilder.Entity<CommunityProgramsToanN>(entity =>
        {
            entity.HasKey(e => e.ProgramToanNSID).HasName("PK__Communit__CB59144E069E13A4");

            entity.ToTable("CommunityProgramsToanNS");

            entity.HasIndex(e => e.Status, "IDX_CommunityProgramsToanNS_Status");

            entity.Property(e => e.CurrentParticipants).HasDefaultValue(0);
            entity.Property(e => e.EndDate).HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Location).HasMaxLength(255);
            entity.Property(e => e.ProgramName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.StartDate).HasColumnType("datetime");
            entity.Property(e => e.Status)
                .IsRequired()
                .HasMaxLength(20);

            entity.HasOne(d => d.Organizer).WithMany(p => p.CommunityProgramsToanNs)
                .HasForeignKey(d => d.OrganizerId)
                .HasConstraintName("FK__Community__Organ__571DF1D5");
        });

        modelBuilder.Entity<ConsultantScheduleTrongLH>(entity =>
        {
            entity.HasKey(e => e.ScheduleTrongLHID).HasName("PK__Consulta__33BE882E71BC2A1A");

            entity.ToTable("ConsultantScheduleTrongLH");

            entity.HasIndex(e => new { e.ConsultantID, e.DayOfWeek, e.StartTime, e.EndTime, e.EffectiveFrom }, "UC_ConsultantSchedule").IsUnique();

            entity.Property(e => e.BufferMinutesBetweenMeetings).HasDefaultValue(15);
            entity.Property(e => e.EffectiveFrom).HasDefaultValueSql("(getdate())");
            entity.Property(e => e.IsAvailable).HasDefaultValue(true);
            entity.Property(e => e.MaxAppointmentsPerSlot).HasDefaultValue(1);
            entity.Property(e => e.Notes).HasColumnType("ntext");
            entity.Property(e => e.RecurringPattern)
                .HasMaxLength(50)
                .HasDefaultValue("Weekly");

            entity.HasOne(d => d.Consultant).WithMany(p => p.ConsultantScheduleTrongLHs)
                .HasForeignKey(d => d.ConsultantID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Consultan__Consu__7F2BE32F");
        });

        modelBuilder.Entity<ConsultantsTrongLH>(entity =>
        {
            entity.HasKey(e => e.ConsultantTrongLHID).HasName("PK__Consulta__0D38FD8FAAADD056");

            entity.ToTable("ConsultantsTrongLH");

            entity.HasIndex(e => e.IsAvailable, "IDX_ConsultantsTrongLH_IsAvailable");

            entity.HasIndex(e => e.Specialization, "IDX_ConsultantsTrongLH_Specialization");

            entity.Property(e => e.AverageRating)
                .HasDefaultValue(0m)
                .HasColumnType("decimal(3, 2)");
            entity.Property(e => e.Certification).HasMaxLength(255);
            entity.Property(e => e.IsAvailable).HasDefaultValue(true);
            entity.Property(e => e.ProfileVerified).HasDefaultValue(false);
            entity.Property(e => e.Qualification).HasMaxLength(255);
            entity.Property(e => e.Specialization)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.TotalConsultations).HasDefaultValue(0);

            entity.HasOne(d => d.User).WithMany(p => p.ConsultantsTrongLHs)
                .HasForeignKey(d => d.UserID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Consultan__UserI__47DBAE45");
        });

        modelBuilder.Entity<CoursesQuangTNV>(entity =>
        {
            entity.HasKey(e => e.CourseQuangTNVID).HasName("PK__CoursesQ__122D2620D8B95D21");

            entity.ToTable("CoursesQuangTNV");

            entity.HasIndex(e => e.AgeGroup, "IDX_CoursesQuangTNV_AgeGroup");

            entity.HasIndex(e => e.Category, "IDX_CoursesQuangTNV_Category");

            entity.HasIndex(e => e.IsActive, "IDX_CoursesQuangTNV_IsActive");

            entity.Property(e => e.AgeGroup)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.Category)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.ContentURL).HasMaxLength(255);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.InstructorName).HasMaxLength(100);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.Rating)
                .HasDefaultValue(0m)
                .HasColumnType("decimal(3, 2)");
            entity.Property(e => e.Title)
                .IsRequired()
                .HasMaxLength(100);
        });

        modelBuilder.Entity<OrganizationProgramsTuyenTM>(entity =>
        {
            entity.HasKey(e => e.OrganizationProgramTuyenTMID).HasName("PK__Organiza__9C1847E416E2F6E2");

            entity.ToTable("OrganizationProgramsTuyenTM");

            entity.Property(e => e.ContributionDescription).HasColumnType("ntext");
            entity.Property(e => e.FundingAmount).HasColumnType("decimal(18, 2)");
            entity.Property(e => e.IsOrganizer).HasDefaultValue(false);
            entity.Property(e => e.IsSponsor).HasDefaultValue(false);
            entity.Property(e => e.JoinedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastUpdated)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ProgramRole).HasMaxLength(100);

            entity.HasOne(d => d.Organization).WithMany(p => p.OrganizationProgramsTuyenTMs)
                .HasForeignKey(d => d.OrganizationID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Organizat__Organ__55F4C372");

            entity.HasOne(d => d.ProgramToanNS).WithMany(p => p.OrganizationProgramsTuyenTMs)
                .HasForeignKey(d => d.ProgramToanNSID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Organizat__Progr__56E8E7AB");
        });

        modelBuilder.Entity<OrganizationsTuyenTM>(entity =>
        {
            entity.HasKey(e => e.OrganizationTuyenTMID).HasName("PK__Organiza__123C3A5728C956F3");

            entity.ToTable("OrganizationsTuyenTM");

            entity.Property(e => e.Address).HasMaxLength(255);
            entity.Property(e => e.ContactEmail).HasMaxLength(100);
            entity.Property(e => e.ContactPhone).HasMaxLength(20);
            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsPartner).HasDefaultValue(false);
            entity.Property(e => e.OrganizationName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.Type)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.Website).HasMaxLength(255);
        });

        modelBuilder.Entity<ProgramParticipantsToanN>(entity =>
        {
            entity.HasKey(e => e.ParticipantToanNSID).HasName("PK__ProgramP__C3891D6C84EC855B");

            entity.ToTable("ProgramParticipantsToanNS");

            entity.Property(e => e.AttendanceStatus).HasMaxLength(20);
            entity.Property(e => e.CertificateIssued).HasDefaultValue(false);
            entity.Property(e => e.FeedbackComments).HasColumnType("ntext");
            entity.Property(e => e.FeedbackProvided).HasDefaultValue(false);
            entity.Property(e => e.ParticipantRole).HasMaxLength(50);
            entity.Property(e => e.RegistrationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.ProgramToanNS).WithMany(p => p.ProgramParticipantsToanNs)
                .HasForeignKey(d => d.ProgramToanNSID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__ProgramPa__Progr__6FE99F9F");

            entity.HasOne(d => d.User).WithMany(p => p.ProgramParticipantsToanNs)
                .HasForeignKey(d => d.UserID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__ProgramPa__UserI__70DDC3D8");
        });

        modelBuilder.Entity<SurveyQuestionsQuangTNV>(entity =>
        {
            entity.HasKey(e => e.QuestionQuangTNVID).HasName("PK__SurveyQu__8B30E7C0CD286B5D");

            entity.ToTable("SurveyQuestionsQuangTNV");

            entity.Property(e => e.DependsOnAnswer).HasMaxLength(255);
            entity.Property(e => e.HelpText).HasColumnType("ntext");
            entity.Property(e => e.ImageURL).HasMaxLength(255);
            entity.Property(e => e.IsRequired).HasDefaultValue(true);
            entity.Property(e => e.Options).HasColumnType("ntext");
            entity.Property(e => e.QuestionText)
                .IsRequired()
                .HasColumnType("ntext");
            entity.Property(e => e.QuestionType)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.RiskWeight).HasDefaultValue(1);

            entity.HasOne(d => d.Survey).WithMany(p => p.SurveyQuestionsQuangTNVs)
                .HasForeignKey(d => d.SurveyID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__SurveyQue__Surve__787EE5A0");
        });

        modelBuilder.Entity<SurveysNamND>(entity =>
        {
            entity.HasKey(e => e.SurveyNamNDID).HasName("PK__SurveysN__A48BA58E621B35B7");

            entity.ToTable("SurveysNamND");

            entity.HasIndex(e => e.IsActive, "IDX_SurveysNamND_IsActive");

            entity.Property(e => e.CreatedDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.IsAnonymous).HasDefaultValue(false);
            entity.Property(e => e.SurveyName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.SurveyType)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.TargetAudience).HasMaxLength(50);
        });

        modelBuilder.Entity<System_UserAccount>(entity =>
        {
            entity.HasKey(e => e.UserAccountID);

            entity.ToTable("System.UserAccount");

            entity.Property(e => e.ApplicationCode).HasMaxLength(50);
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Email)
                .IsRequired()
                .HasMaxLength(150);
            entity.Property(e => e.EmployeeCode)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.FullName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.ModifiedBy).HasMaxLength(50);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Password)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.Phone)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.RequestCode).HasMaxLength(50);
            entity.Property(e => e.UserName)
                .IsRequired()
                .HasMaxLength(50);
        });

        modelBuilder.Entity<UserAppointmentsNganVHH>(entity =>
        {
            entity.HasKey(e => e.UserAppointmentNganVHHID).HasName("PK__UserAppo__FB1589F14B22FE38");

            entity.ToTable("UserAppointmentsNganVHH");

            entity.Property(e => e.AdditionalNotes).HasColumnType("ntext");
            entity.Property(e => e.BookingDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FollowUpNotes).HasColumnType("ntext");
            entity.Property(e => e.FollowUpRequired).HasDefaultValue(false);
            entity.Property(e => e.PostAppointmentFeedbackProvided).HasDefaultValue(false);
            entity.Property(e => e.PreAppointmentSurveyCompleted).HasDefaultValue(false);
            entity.Property(e => e.PrimaryReason).HasMaxLength(255);

            entity.HasOne(d => d.Appointment).WithMany(p => p.UserAppointmentsNganVHHs)
                .HasForeignKey(d => d.AppointmentID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserAppoi__Appoi__693CA210");
        });

        modelBuilder.Entity<UserCoursesTuyenTM>(entity =>
        {
            entity.HasKey(e => e.UserCourseTuyenTMID).HasName("PK__UserCour__ACC29419CD8940AF");

            entity.ToTable("UserCoursesTuyenTM");

            entity.Property(e => e.CertificateIssued).HasDefaultValue(false);
            entity.Property(e => e.CompletionDate).HasColumnType("datetime");
            entity.Property(e => e.EnrollmentDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastAccessDate).HasColumnType("datetime");
            entity.Property(e => e.Progress)
                .HasDefaultValue(0m)
                .HasColumnType("decimal(5, 2)");

            entity.HasOne(d => d.Course).WithMany(p => p.UserCoursesTuyenTMs)
                .HasForeignKey(d => d.CourseID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserCours__Cours__5BE2A6F2");

            entity.HasOne(d => d.User).WithMany(p => p.UserCoursesTuyenTMs)
                .HasForeignKey(d => d.UserID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserCours__UserI__5AEE82B9");
        });

        modelBuilder.Entity<UserSurveysNamND>(entity =>
        {
            entity.HasKey(e => e.UserSurveyNamNDID).HasName("PK__UserSurv__7AAF6C20F38B5266");

            entity.ToTable("UserSurveysNamND");

            entity.Property(e => e.IsAnonymous).HasDefaultValue(false);
            entity.Property(e => e.Recommendations).HasColumnType("ntext");
            entity.Property(e => e.ResultSummary).HasColumnType("ntext");
            entity.Property(e => e.SubmissionDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");

            entity.HasOne(d => d.Survey).WithMany(p => p.UserSurveysNamNDs)
                .HasForeignKey(d => d.SurveyID)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__UserSurve__Surve__6383C8BA");

            entity.HasOne(d => d.User).WithMany(p => p.UserSurveysNamNDs)
                .HasForeignKey(d => d.UserID)
                .HasConstraintName("FK__UserSurve__UserI__628FA481");
        });

        modelBuilder.Entity<UsersTuyenTM>(entity =>
        {
            entity.HasKey(e => e.UserTuyenTMID).HasName("PK__UsersTuy__1042E1E0702A1716");

            entity.ToTable("UsersTuyenTM");

            entity.HasIndex(e => e.IsActive, "IDX_UsersTuyenTM_IsActive");

            entity.HasIndex(e => e.Role, "IDX_UsersTuyenTM_Role");

            entity.Property(e => e.Email)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.FirstName).HasMaxLength(50);
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.LastName).HasMaxLength(50);
            entity.Property(e => e.Password)
                .IsRequired()
                .HasMaxLength(255);
            entity.Property(e => e.PhoneNumber).HasMaxLength(20);
            entity.Property(e => e.RegistrationDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Role)
                .IsRequired()
                .HasMaxLength(20);
            entity.Property(e => e.Username)
                .IsRequired()
                .HasMaxLength(50);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}