﻿@page "/Account/Logout"
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies
@inject NavigationManager Navigation
@inject IHttpContextAccessor HttpContextAccessor

<header>
    <h1>Log out</h1>
    <p>You have successfully logged out of the application.</p>
</header>

@code {
    protected override async Task OnInitializedAsync()
    {
        // Sign out the user
        await HttpContextAccessor.HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);

        // Remove the UserName cookie
        HttpContextAccessor.HttpContext.Response.Cookies.Delete("UserName");

        // Redirect to the login page
        Navigation.NavigateTo("/Account/Login", forceLoad: true);
    }
}