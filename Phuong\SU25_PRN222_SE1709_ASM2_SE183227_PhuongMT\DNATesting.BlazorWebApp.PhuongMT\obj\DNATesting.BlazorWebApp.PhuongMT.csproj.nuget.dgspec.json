{"format": 1, "restore": {"D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\DNATesting.BlazorWebApp.PhuongMT.csproj": {}}, "projects": {"D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\DNATesting.BlazorWebApp.PhuongMT.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\DNATesting.BlazorWebApp.PhuongMT.csproj", "projectName": "DNATesting.BlazorWebApp.PhuongMT", "projectPath": "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\DNATesting.BlazorWebApp.PhuongMT.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.BlazorWebApp.PhuongMT\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Services.PhuongMT\\DNATesting.Services.PhuongMT.csproj": {"projectPath": "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Services.PhuongMT\\DNATesting.Services.PhuongMT.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Repositories.PhuongMT\\DNATesting.Repositories.PhuongMT.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Repositories.PhuongMT\\DNATesting.Repositories.PhuongMT.csproj", "projectName": "DNATesting.Repositories.PhuongMT", "projectPath": "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Repositories.PhuongMT\\DNATesting.Repositories.PhuongMT.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Repositories.PhuongMT\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.5, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Services.PhuongMT\\DNATesting.Services.PhuongMT.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Services.PhuongMT\\DNATesting.Services.PhuongMT.csproj", "projectName": "DNATesting.Services.PhuongMT", "projectPath": "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Services.PhuongMT\\DNATesting.Services.PhuongMT.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Services.PhuongMT\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Repositories.PhuongMT\\DNATesting.Repositories.PhuongMT.csproj": {"projectPath": "D:\\FPTU\\S7\\PRN222\\Phuong\\SU25_PRN222_SE1709_ASM2_SE183227_PhuongMT\\DNATesting.Repositories.PhuongMT\\DNATesting.Repositories.PhuongMT.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}