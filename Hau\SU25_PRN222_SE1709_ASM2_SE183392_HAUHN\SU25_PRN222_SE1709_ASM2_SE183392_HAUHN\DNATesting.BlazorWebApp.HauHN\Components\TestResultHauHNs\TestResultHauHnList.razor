﻿@page "/TestResultHauHNs/TestResultHauHnList"
@using DNATesting.Repositories.HauHN.Models
@using Microsoft.AspNetCore.Authorization

@attribute [Authorize(Roles = "1,2,3")]

<div class="medical-container">
    <div class="container-fluid p-4">
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="page-title">
                        <h2 class="mb-0">
                            <i class="bi bi-dna me-2"></i>
                            DNA Test Results
                        </h2>
                        <p class="text-muted mb-0">Manage and monitor DNA test results</p>
                    </div>
                    <a href="/TestResultHauHNs/TestResultHauHnForm"
                       class="btn btn-primary" title="New Test Result">
                        <i class="bi bi-plus-circle me-2"> New Test Result</i>
                    </a>
                </div>
            </div>
        </div>

        <div class="card main-card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-clipboard2-pulse fs-4 me-2 text-primary"></i>
                            <h5 class="mb-0">Test Results Overview</h5>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-md-end mt-3 mt-md-0">
                            <div class="search-box me-2">
                                <i class="bi bi-search"></i>
                                <input type="text" @bind="searchResultId" @bind:event="oninput" 
                                       @onkeyup="HandleSearch" class="form-control" placeholder="Search Result ID...">
                            </div>
                            <div class="search-box me-2">
                                <i class="bi bi-search"></i>
                                <input type="text" @bind="searchAppointmentId" @bind:event="oninput" 
                                       @onkeyup="HandleSearch" class="form-control" placeholder="Search Appointment ID...">
                            </div>
                            <select class="form-select" @onchange="OnStatusChanged">
                                <option value="" selected="@(searchIsVerified == "")">All Status</option>
                                <option value="true" selected="@(searchIsVerified == "true")">Verified</option>
                                <option value="false" selected="@(searchIsVerified == "false")">Pending</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card-body p-0">
                @if (testResultHauHNs == null)
                {
                    <div class="loading-container">
                        <div class="loading-spinner"></div>
                        <p class="loading-text">Loading test results...</p>
                    </div>
                }
                else
                {
                    <div class="table-responsive">
                        <table class="table table-hover medical-table mb-0">
                            <thead>
                                <tr>
                                    <th class="col-id"><i class="bi bi-fingerprint me-2"></i>Result ID</th>
                                    <th><i class="bi bi-calendar-event me-2"></i>Appointment ID</th>
                                    <th class="col-type"><i class="bi bi-virus me-2"></i>Test Type</th>
                                    <th class="col-value"><i class="bi bi-clipboard-data me-2"></i>Result</th>
                                    <th><i class="bi bi-calendar2-check me-2"></i>Result Date</th>
                                    <th class="col-date"><i class="bi bi-calendar-check me-2"></i>Test Date</th>
                                    <th class="col-tech"><i class="bi bi-person-badge me-2"></i>Technician</th>
                                    <th><i class="bi bi-person-check me-2"></i>Verified By</th>
                                    <th><i class="bi bi-shield-check me-2"></i>Status</th>
                                    <th><i class="bi bi-bug me-2"></i>Genetic Mutation</th>
                                    <th><i class="bi bi-activity me-2"></i>Disease Risk</th>
                                    <th class="col-prob"><i class="bi bi-graph-up me-2"></i>Probability</th>
                                    <th class="col-actions text-end"><i class="bi bi-sliders me-2"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var testresult in CurrentPageItems)
                                {
                                    <tr class="align-middle">
                                        <td class="col-id fw-medium">@testresult.ResultHauHnid</td>
                                        <td>@testresult.AppointmentHanNhmid</td>
                                        <td class="col-type">
                                            <span class="badge medical-badge-type">
                                                <i class="bi bi-virus me-1"></i>
                                                @testresult.TestType
                                            </span>
                                        </td>
                                        <td class="col-value">@testresult.ResultValue</td>
                                        <td>@testresult.ResultDate?.ToString("dd/MM/yyyy")</td>
                                        <td class="col-date">
                                            <i class="bi bi-calendar3 me-1 text-muted"></i>
                                            @testresult.TestDate?.ToString("dd/MM/yyyy")
                                        </td>
                                        <td class="col-tech">
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-2">
                                                    <i class="bi bi-person"></i>
                                                </div>
                                                @testresult.TechnicianId
                                            </div>
                                        </td>
                                        <td>@testresult.VerifiedBy</td>
                                        <td>
                                            @if (testresult.IsVerified)
                                            {
                                                <span class="badge medical-badge-verified">
                                                    <i class="bi bi-check-circle-fill me-1"></i>Verified
                                                </span>
                                            }
                                            else
                                            {
                                                <span class="badge medical-badge-pending">
                                                    <i class="bi bi-clock-fill me-1"></i>Pending
                                                </span>
                                            }
                                        </td>
                                        <td>@testresult.GeneticMutation</td>
                                        <td>@testresult.DiseaseRisk</td>
                                        <td class="col-prob">
                                            <div class="d-flex align-items-center">
                                                <span class="ms-2 fw-medium">@testresult.RelationshipProbability%</span>
                                            </div>
                                        </td>
                                        <td class="col-actions text-end">
                                            <div class="action-buttons">
                                                <a href="/TestResultHauHNs/TestResultHauHnDetail/@testresult.ResultHauHnid"
                                                   class="btn btn-sm btn-light-primary me-1" title="View Details">
                                                    <i class="bi bi-eye"></i>
                                                </a>
                                                <a href="/TestResultHauHNs/TestResultHauHnForm/@testresult.ResultHauHnid"
                                                   class="btn btn-sm btn-light-success" title="Edit">
                                                    <i class="bi bi-pencil"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="pagination-info">
                                Showing @((CurrentPage - 1) * PageSize + 1) to @Math.Min(CurrentPage * PageSize, testResultHauHNs.Count) of @testResultHauHNs.Count entries
                            </div>
                            <nav>
                                <ul class="pagination mb-0">
                                    <li class="page-item @(CurrentPage == 1 ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => NavigateToPage(CurrentPage - 1)" disabled="@(CurrentPage == 1)">
                                            <i class="bi bi-chevron-left"></i>
                                        </button>
                                    </li>

                                    @for (int i = StartPage; i <= EndPage; i++)
                                    {
                                        var pageNumber = i;
                                        <li class="page-item @(pageNumber == CurrentPage ? "active" : "")">
                                            <button class="page-link" @onclick="() => NavigateToPage(pageNumber)">@pageNumber</button>
                                        </li>
                                    }

                                    <li class="page-item @(CurrentPage == TotalPages ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => NavigateToPage(CurrentPage + 1)" disabled="@(CurrentPage == TotalPages)">
                                            <i class="bi bi-chevron-right"></i>
                                        </button>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    .medical-container {
        background-color: #f0f4f8;
        min-height: 100vh;
        width: 100%;
    }

    .container-fluid {
        max-width: 100%;
        width: 100%;
        padding-right: 15px;
        padding-left: 15px;
        box-sizing: border-box;
    }

    .page-title h2 {
        color: #1a365d;
        font-weight: 700;
        letter-spacing: -0.5px;
    }

    .main-card {
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        background: #ffffff;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }

    .card-header {
        background: #ffffff;
        border-bottom: 2px solid #e2e8f0;
        padding: 1.5rem;
    }

    .search-box {
        position: relative;
        width: 100%;
        max-width: 300px;
    }

    .search-box i {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #4a5568;
    }

    .search-box input {
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border-radius: 6px;
        border: 1px solid #e2e8f0;
        background-color: #f8fafc;
        transition: all 0.2s;
    }

    .search-box input:focus {
        background-color: #ffffff;
        border-color: #3182ce;
        box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
    }

    .medical-table {
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        table-layout: auto;
    }

    .medical-table th {
        background: #f8fafc;
        color: #2d3748;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        padding: 1rem;
        border-top: none;
        letter-spacing: 0.05em;
    }

    .medical-table td {
        padding: 1rem;
        border-color: #e2e8f0;
        color: #4a5568;
        word-break: break-word;
    }

    .medical-table tr:hover {
        background-color: #f8fafc;
    }

    .medical-badge-type {
        background-color: #2b6cb0;
        color: white;
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
        border-radius: 4px;
        font-weight: 500;
    }

    .medical-badge-verified {
        background-color: #38a169;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-weight: 500;
    }

    .medical-badge-pending {
        background-color: #d69e2e;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        font-weight: 500;
    }

    .loading-container {
        padding: 4rem;
        text-align: center;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e2e8f0;
        border-top: 3px solid #2b6cb0;
        border-radius: 50%;
        margin: 0 auto;
        -webkit-animation: spinner 1s linear infinite;
        animation: spinner 1s linear infinite;
    }

    @@-webkit-keyframes spinner {
        0% { -webkit-transform: rotate(0deg); }
        100% { -webkit-transform: rotate(360deg); }
    }

    @@keyframes spinner {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .loading-text {
        color: #4a5568;
        margin-top: 1rem;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
    }

    .card-body {
        padding: 0;
    }

    @@media (max-width: 1200px) {
        .col-genetic, .col-prob, .col-actions {
            display: none;
        }
    }
    @@media (max-width: 992px) {
        .col-tech, .col-date, .col-value, .col-type, .col-id {
            display: none;
        }
    }
    @@media (max-width: 768px) {
        .container-fluid {
            padding: 5px;
        }
        .card-header {
            padding: 0.5rem;
        }
        .medical-table th,
        .medical-table td {
            padding: 0.5rem;
            font-size: 0.85rem;
        }
        .col-verified, .col-status, .col-disease {
            display: none;
        }
    }

    .card-footer {
        background: #ffffff;
        border-top: 2px solid #e2e8f0;
        padding: 1rem 1.5rem;
    }

    .pagination {
        margin: 0;
    }

    .page-link {
        color: #2b6cb0;
        border: 1px solid #e2e8f0;
        padding: 0.5rem 0.75rem;
        margin: 0 0.25rem;
        border-radius: 4px;
        transition: all 0.2s;
    }

    .page-link:hover {
        background-color: #ebf4ff;
        border-color: #2b6cb0;
        color: #2b6cb0;
    }

    .page-item.active .page-link {
        background-color: #2b6cb0;
        border-color: #2b6cb0;
        color: white;
    }

    .page-item.disabled .page-link {
        color: #a0aec0;
        pointer-events: none;
        background-color: #f8fafc;
        border-color: #e2e8f0;
    }

    .pagination-info {
        color: #4a5568;
        font-size: 0.875rem;
    }

    @@media (max-width: 768px) {
        .pagination-info {
            display: none;
        }
    }
</style>

@code {
    private List<TestResultHauHn> testResultHauHNs;
    private string searchResultId = "";
    private string searchAppointmentId = "";
    private string searchIsVerified = "";

    // Pagination parameters
    private int CurrentPage = 1;
    private const int PageSize = 5;
    private const int MaxPagesShown = 5;

    private int TotalPages => testResultHauHNs == null ? 0 : (int)Math.Ceiling(testResultHauHNs.Count / (double)PageSize);
    
    private int StartPage
    {
        get
        {
            var halfWay = (int)Math.Floor(MaxPagesShown / 2d);
            if (TotalPages <= MaxPagesShown)
                return 1;
            if (CurrentPage <= halfWay)
                return 1;
            if (CurrentPage + halfWay >= TotalPages)
                return TotalPages - MaxPagesShown + 1;
            return CurrentPage - halfWay;
        }
    }

    private int EndPage => Math.Min(StartPage + MaxPagesShown - 1, TotalPages);

    private IEnumerable<TestResultHauHn> CurrentPageItems =>
        testResultHauHNs?.Skip((CurrentPage - 1) * PageSize).Take(PageSize) ?? Enumerable.Empty<TestResultHauHn>();

    private void NavigateToPage(int page)
    {
        if (page >= 1 && page <= TotalPages)
        {
            CurrentPage = page;
            StateHasChanged();
        }
    }

    protected override async Task OnInitializedAsync()
    {
        testResultHauHNs = null;
        await Task.Delay(500);
        try 
        {
            testResultHauHNs = await ServiceProviders.testResultHauHNService.GetAllAsync();
            CurrentPage = 1; // Reset to first page when loading new data
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading test results: {ex.Message}");
        }
    }

    private async Task HandleSearch()
    {
        try
        {
            bool? isVerified = null;
            if (!string.IsNullOrEmpty(searchIsVerified))
            {
                isVerified = bool.Parse(searchIsVerified);
            }

            // Nếu tất cả các trường search đều trống, load all data
            if (string.IsNullOrEmpty(searchResultId) && string.IsNullOrEmpty(searchAppointmentId) && string.IsNullOrEmpty(searchIsVerified))
            {
                testResultHauHNs = await ServiceProviders.testResultHauHNService.GetAllAsync();
            }
            else
            {
                testResultHauHNs = await ServiceProviders.testResultHauHNService.SearchAsync(
                    searchResultId,
                    isVerified,
                    searchAppointmentId
                );
            }
            CurrentPage = 1; // Reset to first page after search
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error searching test results: {ex.Message}");
        }
    }

    private async Task OnStatusChanged(ChangeEventArgs e)
    {
        searchIsVerified = e.Value?.ToString() ?? "";
        await HandleSearch();
    }
}
