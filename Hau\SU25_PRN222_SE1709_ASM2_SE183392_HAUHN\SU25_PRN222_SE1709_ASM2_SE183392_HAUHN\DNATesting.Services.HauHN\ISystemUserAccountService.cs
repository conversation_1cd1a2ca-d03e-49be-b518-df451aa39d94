﻿using DNATesting.Repositories.HauHN.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.HauHN
{
    public interface ISystemUserAccountService
    {
        Task<SystemUserAccount> GetUserAccountAsync(string userName, string password);
        Task<List<SystemUserAccount>> GetAllUserAccountAsync();
        Task<SystemUserAccount> GetUserAccountByUserNameAsync(string userName);
    }
}
