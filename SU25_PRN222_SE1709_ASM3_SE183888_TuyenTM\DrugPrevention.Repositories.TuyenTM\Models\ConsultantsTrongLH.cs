﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DrugPrevention.Repositories.TuyenTM.Models;

public partial class ConsultantsTrongLH
{
    public int ConsultantTrongLHID { get; set; }

    public int UserID { get; set; }

    public string Specialization { get; set; }

    public string Qualification { get; set; }

    public string Certification { get; set; }

    public int? YearsOfExperience { get; set; }

    public decimal? AverageRating { get; set; }

    public int? TotalConsultations { get; set; }

    public bool? IsAvailable { get; set; }

    public bool? ProfileVerified { get; set; }

    public virtual ICollection<ConsultantScheduleTrongLH> ConsultantScheduleTrongLHs { get; set; } = new List<ConsultantScheduleTrongLH>();

    public virtual UsersTuyenTM User { get; set; }
}