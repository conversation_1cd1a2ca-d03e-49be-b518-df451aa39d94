﻿@using System.Security.Claims

@inherits LayoutComponentBase

<div class="page">
    @if (!IsLoginPage)
    {
        <div class="sidebar">
            <NavMenu />
        </div>
    }

    <main class="@(!IsLoginPage ? "" : "w-100")">
        <div class="top-row">
            @if (user?.Identity?.IsAuthenticated ?? false)
            {
                <span class="me-3 text-primary">👤 @user.Identity.Name</span>
                <button class="btn btn-link" @onclick="Logout">Logout</button>
            }
            <a href="https://learn.microsoft.com/aspnet/core/" target="_blank">About</a>
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    private bool IsLoginPage => navigationManager.Uri.Contains("/Account/Login", StringComparison.OrdinalIgnoreCase);
    private ClaimsPrincipal user;

    protected override async Task OnInitializedAsync()  
	{
        var authState = await authenticationStateProvider.GetAuthenticationStateAsync();
        user = authState.User;
	}

    private async Task Logout()
    {
        await runtimeJS.InvokeVoidAsync("eval", $"window.location.href = '/Account/Logout'");
    }
}