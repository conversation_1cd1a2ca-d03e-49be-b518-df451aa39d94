﻿using DNATesting.Repositories.HauHN.Basic;
using DNATesting.Repositories.HauHN.DBContext;
using DNATesting.Repositories.HauHN.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Repositories.HauHN
{
    public class TestResultDetailsHauHnRepository : GenericRepository<TestResultDetailsHauHn>
    {
        public TestResultDetailsHauHnRepository() { }
        public TestResultDetailsHauHnRepository(SE18_PRN222_SE1709_G3_DNA_TestingContext context) => _context = context;


    }
}
