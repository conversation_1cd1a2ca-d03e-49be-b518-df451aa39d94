{"Version": 1, "Hash": "qEXTWK9osVnLk9ewMJFcCNSyVAy5fwdDXpyxjL1DOt8=", "Source": "DNATesting.BlazorWebApp.HauHN", "BasePath": "_content/DNATesting.BlazorWebApp.HauHN", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "DNATesting.BlazorWebApp.HauHN\\wwwroot", "Source": "DNATesting.BlazorWebApp.HauHN", "ContentRoot": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\", "BasePath": "_content/DNATesting.BlazorWebApp.HauHN", "Pattern": "**"}], "Assets": [{"Identity": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DNATesting.BlazorWebApp.HauHN.styles.css", "SourceId": "DNATesting.BlazorWebApp.HauHN", "SourceType": "Computed", "ContentRoot": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/DNATesting.BlazorWebApp.HauHN", "RelativePath": "DNATesting.BlazorWebApp.HauHN#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "g3uftwdgky", "Integrity": "HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DNATesting.BlazorWebApp.HauHN.styles.css", "FileLength": 5941, "LastWriteTime": "2025-06-16T10:23:27+00:00"}, {"Identity": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DNATesting.BlazorWebApp.HauHN.bundle.scp.css", "SourceId": "DNATesting.BlazorWebApp.HauHN", "SourceType": "Computed", "ContentRoot": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/DNATesting.BlazorWebApp.HauHN", "RelativePath": "DNATesting.BlazorWebApp.HauHN#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "g3uftwdgky", "Integrity": "HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DNATesting.BlazorWebApp.HauHN.bundle.scp.css", "FileLength": 5941, "LastWriteTime": "2025-06-16T10:23:27+00:00"}, {"Identity": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\app.css", "SourceId": "DNATesting.BlazorWebApp.HauHN", "SourceType": "Discovered", "ContentRoot": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\", "BasePath": "_content/DNATesting.BlazorWebApp.HauHN", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "73zgofhkbc", "Integrity": "XSalpy5Yk5nPT8Ff/GQ6OMLaE2GUDC4N26XNyB3J66U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 3522, "LastWriteTime": "2025-06-15T15:07:01+00:00"}, {"Identity": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\bootstrap\\bootstrap.min.css", "SourceId": "DNATesting.BlazorWebApp.HauHN", "SourceType": "Discovered", "ContentRoot": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\", "BasePath": "_content/DNATesting.BlazorWebApp.HauHN", "RelativePath": "bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-06-12T08:52:21+00:00"}, {"Identity": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\bootstrap\\bootstrap.min.css.map", "SourceId": "DNATesting.BlazorWebApp.HauHN", "SourceType": "Discovered", "ContentRoot": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\", "BasePath": "_content/DNATesting.BlazorWebApp.HauHN", "RelativePath": "bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-06-12T08:52:21+00:00"}, {"Identity": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\favicon.png", "SourceId": "DNATesting.BlazorWebApp.HauHN", "SourceType": "Discovered", "ContentRoot": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\", "BasePath": "_content/DNATesting.BlazorWebApp.HauHN", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-12T08:52:21+00:00"}, {"Identity": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\images\\original-8e7ce77dec0edaf0105e8287038f6e60.gif", "SourceId": "DNATesting.BlazorWebApp.HauHN", "SourceType": "Discovered", "ContentRoot": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\", "BasePath": "_content/DNATesting.BlazorWebApp.HauHN", "RelativePath": "images/original-8e7ce77dec0edaf0105e8287038f6e60#[.{fingerprint}]?.gif", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e1p5ytybew", "Integrity": "/hslHbN19UnlVHbs+JX1dVEbqXa46bFwG6GMKmJy/Sc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\original-8e7ce77dec0edaf0105e8287038f6e60.gif", "FileLength": 2328104, "LastWriteTime": "2025-06-12T09:38:44+00:00"}], "Endpoints": [{"Route": "app.73zgofhkbc.css", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3522"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XSalpy5Yk5nPT8Ff/GQ6OMLaE2GUDC4N26XNyB3J66U=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 15:07:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "73zgofhkbc"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-XSalpy5Yk5nPT8Ff/GQ6OMLaE2GUDC4N26XNyB3J66U="}]}, {"Route": "app.css", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3522"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XSalpy5Yk5nPT8Ff/GQ6OMLaE2GUDC4N26XNyB3J66U=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 15:07:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XSalpy5Yk5nPT8Ff/GQ6OMLaE2GUDC4N26XNyB3J66U="}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "DNATesting.BlazorWebApp.HauHN.bundle.scp.css", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DNATesting.BlazorWebApp.HauHN.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5941"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 10:23:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs="}]}, {"Route": "DNATesting.BlazorWebApp.HauHN.g3uftwdgky.bundle.scp.css", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DNATesting.BlazorWebApp.HauHN.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5941"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 10:23:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g3uftwdgky"}, {"Name": "label", "Value": "DNATesting.BlazorWebApp.HauHN.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs="}]}, {"Route": "DNATesting.BlazorWebApp.HauHN.g3uftwdgky.styles.css", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DNATesting.BlazorWebApp.HauHN.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5941"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 10:23:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g3uftwdgky"}, {"Name": "label", "Value": "DNATesting.BlazorWebApp.HauHN.styles.css"}, {"Name": "integrity", "Value": "sha256-HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs="}]}, {"Route": "DNATesting.BlazorWebApp.HauHN.styles.css", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DNATesting.BlazorWebApp.HauHN.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5941"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 10:23:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "label", "Value": "favicon.png"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "favicon.png", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "images/original-8e7ce77dec0edaf0105e8287038f6e60.e1p5ytybew.gif", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\images\\original-8e7ce77dec0edaf0105e8287038f6e60.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2328104"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"/hslHbN19UnlVHbs+JX1dVEbqXa46bFwG6GMKmJy/Sc=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 09:38:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e1p5ytybew"}, {"Name": "label", "Value": "images/original-8e7ce77dec0edaf0105e8287038f6e60.gif"}, {"Name": "integrity", "Value": "sha256-/hslHbN19UnlVHbs+JX1dVEbqXa46bFwG6GMKmJy/Sc="}]}, {"Route": "images/original-8e7ce77dec0edaf0105e8287038f6e60.gif", "AssetFile": "D:\\FPT\\Major\\Semester_7\\PRN_222\\SU25_PRN222_SE1709_ASM2_SE183392_HAUHN\\DNATesting.BlazorWebApp.HauHN\\wwwroot\\images\\original-8e7ce77dec0edaf0105e8287038f6e60.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2328104"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"/hslHbN19UnlVHbs+JX1dVEbqXa46bFwG6GMKmJy/Sc=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 09:38:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/hslHbN19UnlVHbs+JX1dVEbqXa46bFwG6GMKmJy/Sc="}]}]}