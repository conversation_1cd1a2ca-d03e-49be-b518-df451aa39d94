﻿@page "/Account/Logout"
@using Microsoft.AspNetCore.Authentication
@using System.Security.Claims
@using Microsoft.AspNetCore.Authentication.Cookies
@using Microsoft.AspNetCore.Http
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider
@inject IHttpContextAccessor HttpContextAccessor

@code {
    protected override async Task OnInitializedAsync()
    {
        // Sign out the user
        await HttpContextAccessor.HttpContext.SignOutAsync();
        // Redirect to the login page
        HttpContextAccessor.HttpContext.Response.Redirect("/Account/Login");
	}
}