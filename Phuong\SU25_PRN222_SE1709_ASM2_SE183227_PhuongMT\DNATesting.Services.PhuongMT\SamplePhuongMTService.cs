﻿using DNATesting.Repositories.PhuongMT;
using DNATesting.Repositories.PhuongMT.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.PhuongMT
{
    public class SamplePhuongMTService : ISamplePhuongMTService
    {

        private readonly UnitOfWork _unitOfWork;
        public SamplePhuongMTService() => _unitOfWork ??= new UnitOfWork();


        public async Task<int> CreateAsync(SamplePhuongMt sample)
        {
            return await _unitOfWork.SamplePhuongMTRepository.CreateAsync(sample);
        }

      
        public async Task<bool> DeleteAsync(SamplePhuongMt sample)
        {
            var term = await _unitOfWork.SamplePhuongMTRepository.GetByIdAsync(sample.SamplePhuongMtid);
            if (term != null)
            {
                return await _unitOfWork.SamplePhuongMTRepository.RemoveAsync(term);
            }
            return false;
        }

        public async Task<bool> DeleteStringAsync(string id)
        {
            return await _unitOfWork.SamplePhuongMTRepository.RemoveString(id);
        }

        public async Task<List<SamplePhuongMt>> GetAllAsync()
        {
            return await _unitOfWork.SamplePhuongMTRepository.GetAllAsync();
        }

        public async Task<SamplePhuongMt> GetByIdAsync(string code)
        {
            return await _unitOfWork.SamplePhuongMTRepository.GetByIdAsync(code);
        }

        public async Task<bool> SamplePhuongMTExistsAsync(string id)
        {
            return await _unitOfWork.SamplePhuongMTRepository.SamplePhuongMTExistsAsync(id);
        }

        public async Task<List<SamplePhuongMt>> SearchAsync(string code, decimal amount, string bankNo)
        {
            return await _unitOfWork.SamplePhuongMTRepository.SearchAsync(code, amount, bankNo);
        }

        public async Task<int> UpdateAsync(SamplePhuongMt sample)
        {
            return await _unitOfWork.SamplePhuongMTRepository.UpdateAsync(sample);
        }

    }
}
