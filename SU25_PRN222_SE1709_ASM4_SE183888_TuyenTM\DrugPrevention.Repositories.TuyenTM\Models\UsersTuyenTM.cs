﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DrugPrevention.Repositories.TuyenTM.Models;

public partial class UsersTuyenTM
{
    public int UserTuyenTMID { get; set; }

    public string Username { get; set; }

    public string Password { get; set; }

    public string Email { get; set; }

    public string FirstName { get; set; }

    public string LastName { get; set; }

    public string Role { get; set; }

    public DateTime RegistrationDate { get; set; }

    public bool IsActive { get; set; }

    public string PhoneNumber { get; set; }

    public virtual ICollection<CommunityProgramsToanN> CommunityProgramsToanNs { get; set; } = new List<CommunityProgramsToanN>();

    public virtual ICollection<ConsultantsTrongLH> ConsultantsTrongLHs { get; set; } = new List<ConsultantsTrongLH>();

    public virtual ICollection<ProgramParticipantsToanN> ProgramParticipantsToanNs { get; set; } = new List<ProgramParticipantsToanN>();

    public virtual ICollection<UserCoursesTuyenTM> UserCoursesTuyenTMs { get; set; } = new List<UserCoursesTuyenTM>();

    public virtual ICollection<UserSurveysNamND> UserSurveysNamNDs { get; set; } = new List<UserSurveysNamND>();
}