﻿@page "/SamplePhuongMts/SamplePhuongMtList"
@using DNATesting.Repositories.PhuongMT.Models;
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

<style>
    .pro-theme-container {
        background-color: #f9fafb;
        padding: 2rem;
        border-radius: 0.75rem;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.05);
        font-family: 'Segoe UI', sans-serif;
    }

    .pro-title {
        color: #111827;
        font-size: 1.75rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        text-align: center;
    }

    .pro-table th {
        background-color: #374151;
        color: #ffffff;
        text-align: center;
        font-weight: 500;
    }

    .pro-table td {
        vertical-align: middle;
        text-align: center;
        background-color: #ffffff;
        color: #1f2937;
    }

    .pro-table tr:hover {
        background-color: #e5e7eb;
        transition: background-color 0.2s ease-in-out;
    }

    .btn-detail {
        background-color: #0ea5e9; /* sky-blue-500 */
        color: #ffffff;
        border: none;
        border-radius: 0.375rem;
        padding: 0.35rem 0.75rem;
        font-size: 0.85rem;
        margin: 0 0.2rem;
        display: inline-flex;
        align-items: center;
        transition: background-color 0.2s ease-in-out, transform 0.1s;
        box-shadow: 0 2px 4px rgba(14, 165, 233, 0.2);
    }

        .btn-detail:hover {
            background-color: #0284c7; /* sky-blue-700 */
            transform: translateY(-1px);
        }

    /* Nút chỉnh sửa - màu vàng cam */
    .btn-edit {
        background-color: #f59e0b; /* amber-500 */
        color: #ffffff;
        border: none;
        border-radius: 0.375rem;
        padding: 0.35rem 0.75rem;
        font-size: 0.85rem;
        margin: 0 0.2rem;
        display: inline-flex;
        align-items: center;
        transition: background-color 0.2s ease-in-out, transform 0.1s;
        box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
    }

        .btn-edit:hover {
            background-color: #d97706; /* amber-700 */
            transform: translateY(-1px);
        }

        .btn-detail i,
        .btn-edit i {
            margin-right: 4px;
        }

    .loading-container {
        text-align: center;
        margin-top: 2rem;
    }

        .loading-container img {
            width: 120px;
        }

    .text-muted {
        color: #6b7280;
    }
</style>

<div class="pro-theme-container">
    <h3 class="pro-title">Sample PhuongMt List</h3>

    @if (isLoading)
    {
        <div class="loading-container">
            <img src="https://cdn.dribbble.com/users/1415337/screenshots/10781083/media/0466184625e53796cfeb7d5c5918dec8.gif" alt="Loading..." />
            <p class="text-muted">Loading data...</p>
        </div>
    }

    else
    {
        <div class="mb-4">
            <div class="row g-2">
                <div class="col-md-3">
                    <input @bind="SamplePhuongMtID" class="form-control" placeholder="Search by ID" />
                </div>
                <div class="col-md-3">
                    <input @bind="AppointmentHanNhmid" class="form-control" placeholder="Search by Appointment" />
                </div>
                <div class="col-md-3">
                    <input @bind="BelongTo" class="form-control" placeholder="Search by BelongTo" />
                </div>
                <div class="col-md-3">
                    <button class="btn btn-primary w-100" @onclick="Search">Search</button>
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table pro-table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Appointment</th>
                        <th>Sample Type</th>
                        <th>Belong To</th>
                        <th>Location</th>
                        <th>Status</th>
                        <th>StorageTemp (°C)</th>
                        <th>Collected By</th>
                        <th>Collected Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @if (samplePhuongMts == null || samplePhuongMts.Count == 0)
                    {
                        <tr>
                            <td colspan="10" class="text-center text-muted">No data found.</td>
                        </tr>
                    }
                    else
                    {
                        @foreach (var sample in pagedSamplePhuongMts)
                        {
                            <tr>
                                <td>@sample.SamplePhuongMtid</td>
                                <td>@sample.AppointmentHanNhmid</td>
                                <td>@sample.SampleType</td>
                                <td>@sample.BelongTo</td>
                                <td>@sample.StorageLocation</td>
                                <td>@sample.Status</td>
                                <td>@sample.StorageTemperature</td>
                                <td>@sample.CollectedBy</td>
                                <td>@sample.CollectedDate?.ToString("yyyy-MM-dd")</td>
                                <td>
                                    <a href="/SamplePhuongMts/SamplePhuongMtDetail/@sample.SamplePhuongMtid" class="btn-detail">
                                        <i class="fas fa-eye"></i> Detail
                                    </a>

                                    <a href="/SamplePhuongMts/SamplePhuongMtForm/@sample.SamplePhuongMtid" class="btn-edit">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                </td>
                            </tr>
                        }
                       
                    }
                </tbody>
            </table>
            @if (totalPages > 1)
            {
                <div class="d-flex justify-content-center align-items-center mt-3">
                    <button class="btn btn-outline-secondary me-2" @onclick="GoToPreviousPage" disabled="@(@currentPage == 1)">
                        <i class="fas fa-chevron-left"></i> Prev
                    </button>

                    <span class="mx-2">Page @currentPage of @totalPages</span>

                    <button class="btn btn-outline-secondary ms-2" @onclick="GoToNextPage" disabled="@(@currentPage == totalPages)">
                        Next <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            }
        </div>
    }

</div>

@code {

    private List<SamplePhuongMt> samplePhuongMts;
    private List<SamplePhuongMt> pagedSamplePhuongMts; // danh sách hiện tại hiển thị trên UI

    private int currentPage = 1;
    private int pageSize = 5;
    private int totalPages = 1;

    private bool isLoading = true;

    private string SamplePhuongMtID { get; set; }
    private string BelongTo { get; set; }
    private string AppointmentHanNhmid { get; set; }

    private List<SamplePhuongMt> allSamplePhuongMts = new(); // dữ liệu gốc

    protected override async Task OnInitializedAsync()
    {
        isLoading = true;
        await Task.Delay(500);
        allSamplePhuongMts = await serviceProviders.samplePhuongMTService.GetAllAsync();
        samplePhuongMts = allSamplePhuongMts;
        UpdatePagination();
        isLoading = false;
    }

    private void UpdatePagination()
    {
        totalPages = (int)Math.Ceiling(samplePhuongMts.Count / (double)pageSize);
        currentPage = Math.Max(1, Math.Min(currentPage, totalPages)); // giới hạn trong phạm vi
        pagedSamplePhuongMts = samplePhuongMts
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToList();
    }

    private async Task Search()
    {
        samplePhuongMts = allSamplePhuongMts;

        if (!string.IsNullOrWhiteSpace(SamplePhuongMtID))
        {
            samplePhuongMts = samplePhuongMts
                .Where(x => x.SamplePhuongMtid != null && x.SamplePhuongMtid.Contains(SamplePhuongMtID, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        if (!string.IsNullOrWhiteSpace(BelongTo))
        {
            samplePhuongMts = samplePhuongMts
                .Where(x => x.BelongTo != null && x.BelongTo.Contains(BelongTo, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        if (!string.IsNullOrWhiteSpace(AppointmentHanNhmid))
        {
            samplePhuongMts = samplePhuongMts
                .Where(x => x.AppointmentHanNhmid != null && x.AppointmentHanNhmid.Contains(AppointmentHanNhmid, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        currentPage = 1;
        UpdatePagination();
    }

    private void GoToPreviousPage()
    {
        if (currentPage > 1)
        {
            currentPage--;
            UpdatePagination();
        }
    }

    private void GoToNextPage()
    {
        if (currentPage < totalPages)
        {
            currentPage++;
            UpdatePagination();
        }
    }

}
