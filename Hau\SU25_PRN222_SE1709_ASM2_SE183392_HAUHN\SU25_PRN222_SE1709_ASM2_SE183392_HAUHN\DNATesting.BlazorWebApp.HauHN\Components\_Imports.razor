﻿@using System.Net.Http
@using System.Net.Http.Json
@using DNATesting.Services.HauHN
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using static Microsoft.AspNetCore.Components.Web.RenderMode
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using DNATesting.BlazorWebApp.HauHN
@using DNATesting.BlazorWebApp.HauHN.Components

/// Add injection
@inject NavigationManager navigationManager
@inject IServiceProviders ServiceProviders
@inject IJSRuntime runtimeJS
@inject AuthenticationStateProvider authenticationStateProvider
