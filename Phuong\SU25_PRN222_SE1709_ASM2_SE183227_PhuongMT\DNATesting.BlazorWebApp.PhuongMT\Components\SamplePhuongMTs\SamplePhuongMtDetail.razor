﻿@page "/SamplePhuongMts/SamplePhuongMtDetail/{SamplePhuongMtID}"
@using DNATesting.Repositories.PhuongMT.Models;
@using Microsoft.AspNetCore.Components

<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

<style>
    .detail-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 2rem;
        border-radius: 1rem;
        background: #ffffff;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
    }

    .detail-title {
        text-align: center;
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 1.5rem;
        color: #1e3a8a; /* Indigo-900 */
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 0.75rem 0;
        border-bottom: 1px solid #e5e7eb;
        font-size: 1rem;
    }

    .detail-item span:first-child {
        font-weight: 600;
        color: #374151; /* Gray-700 */
    }

    .btn-action {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        font-size: 0.9rem;
        padding: 0.4rem 1rem;
        border-radius: 0.375rem;
        text-decoration: none;
        transition: all 0.2s;
        margin-top: 1.5rem;
    }

    .btn-detail {
        background-color: #0ea5e9;
        color: white;
    }

    .btn-detail:hover {
        background-color: #0284c7;
    }

    .btn-edit {
        background-color: #DCC6E0;
        color: white;
        margin-left: 1rem;
    }

    .btn-edit:hover {
        background-color: #DCC6E0;
    }

    .loading-container {
        text-align: center;
        margin-top: 2rem;
    }

    .loading-container img {
        width: 120px;
    }

    .text-muted {
        color: #6b7280;
    }

    .btn-delete {
    background-color: #ef4444; /* red-500 */
    color: white;
    margin-left: 1rem;
}

.btn-delete:hover {
    background-color: #dc2626; /* red-600 */
}
</style>

<div class="detail-container">
    @if (samplePhuongMt == null)
    {
        <div class="loading-container">
            <img src="https://cdn.dribbble.com/users/1415337/screenshots/10781083/media/0466184625e53796cfeb7d5c5918dec8.gif" alt="Loading..." />
            <p class="text-muted">Loading detail...</p>
        </div>
    }
    else
    {
        <h2 class="detail-title">Sample Detail - ID @samplePhuongMt.SamplePhuongMtid</h2>

        <div class="detail-item">
            <span>Sample Type:</span>
            <span>@samplePhuongMt.SampleType</span>
        </div>

        <div class="detail-item">
            <span>Status:</span>
            <span>@samplePhuongMt.Status</span>
        </div>

        <div class="detail-item">
            <span>Location:</span>
            <span>@samplePhuongMt.StorageLocation</span>
        </div>

        <div class="detail-item">
            <span>Collected By:</span>
            <span>@samplePhuongMt.CollectedBy</span>
        </div>

        <div class="detail-item">
            <span>Collected Date:</span>
            <span>@samplePhuongMt.CollectedDate?.ToString("yyyy-MM-dd")</span>
        </div>

        <div class="detail-item">
            <span>Storage Temp (°C):</span>
            <span>@samplePhuongMt.StorageTemperature</span>
        </div>

        <div class="text-center">
            <a href="/SamplePhuongMts/SamplePhuongMtList" class="btn-action btn-detail">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <a href="/SamplePhuongMts/SamplePhuongMtForm/@samplePhuongMt.SamplePhuongMtid" class="btn-action btn-edit">
                <i class="fas fa-edit"></i> Edit Sample
            </a>
            <a @onclick="DeleteSample" class="btn-action btn-delete">
    <i class="fas fa-trash"></i> Delete
</a>
        </div>
    }
</div>

@code {
    [Parameter]
    public string SamplePhuongMtID { get; set; }

    private SamplePhuongMt samplePhuongMt;

    protected override async Task OnInitializedAsync()
    {
        samplePhuongMt = await serviceProviders.samplePhuongMTService.GetByIdAsync(SamplePhuongMtID);
    }
   private async Task DeleteSample()
{
    bool confirmDelete = await JS.InvokeAsync<bool>("confirm", $"Are you sure you want to delete sample ID: {samplePhuongMt.SamplePhuongMtid}?");
    if (confirmDelete)
    {
        await serviceProviders.samplePhuongMTService.DeleteStringAsync(SamplePhuongMtID);
        Navigation.NavigateTo("/SamplePhuongMts/SamplePhuongMtList");
    }
}
}
