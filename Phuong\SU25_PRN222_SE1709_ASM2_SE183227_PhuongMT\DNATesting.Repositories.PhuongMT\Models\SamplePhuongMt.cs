﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DNATesting.Repositories.PhuongMT.Models;

public partial class SamplePhuongMt
{
    public string SamplePhuongMtid { get; set; }

    public string AppointmentHanNhmid { get; set; }

    public string SampleType { get; set; }

    public DateTime? CollectedDate { get; set; }

    public string StorageLocation { get; set; }

    public string Status { get; set; }

    public string Note { get; set; }

    public bool? IsValid { get; set; }

    public decimal? StorageTemperature { get; set; }

    public string CollectedBy { get; set; }

    public string BelongTo { get; set; }

    public virtual AppointmentHanNhm AppointmentHanNhm { get; set; }

    public virtual TestParticipantsHauHn BelongToNavigation { get; set; }

    public virtual ICollection<ProcessLogPhuongMt> ProcessLogPhuongMts { get; set; } = new List<ProcessLogPhuongMt>();

    public virtual ICollection<TestResultDetailsHauHn> TestResultDetailsHauHns { get; set; } = new List<TestResultDetailsHauHn>();

    public virtual ICollection<TestResultHauHn> ResultHauHns { get; set; } = new List<TestResultHauHn>();
}