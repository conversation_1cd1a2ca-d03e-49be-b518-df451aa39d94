﻿@page
@model DrugPrevention.RazorWebApp.TuyenTM.Pages.OrganizationProgramTuyenTM.IndexModel

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="mb-0">Organization Program List</h2>
        <a asp-page="Create" class="btn btn-primary">+ Create New</a>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-bordered table-hover align-middle text-center">
            <thead class="table-dark">
                <tr>
                    <th>Joined Date</th>
                    <th>Contribution</th>
                    <th>Sponsor</th>
                    <th>Organizer</th>
                    <th>Role</th>
                    <th>Funding</th>
                    <th>Feedback</th>
                    <th>Last Updated</th>
                    <th>Organization</th>
                    <th>Program</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody id="OrganizationProgramTuyenTMBodyID">
                @foreach (var item in Model.OrganizationProgramsTuyenTM)
                {
                    <tr id="@item.OrganizationProgramTuyenTMID">
                        <td>@item.JoinedDate.ToString("HH:mm dd/MM/yyyy")</td>
                        <td>@item.ContributionDescription</td>
                        <td><span class="badge @(item.IsSponsor == true ? "bg-info text-dark" : "bg-secondary")">@(item.IsSponsor == true ? "Yes" : "No")</span></td>
                        <td><span class="badge @(item.IsOrganizer == true ? "bg-info text-dark" : "bg-secondary")">@(item.IsOrganizer == true ? "Yes" : "No")</span></td>
                        <td>@item.ProgramRole</td>
                        <td>@(item.FundingAmount.HasValue ? item.FundingAmount.Value.ToString("N0") + " VNĐ" : "None")</td>
                        <td>@item.Feedback</td>
                        <td>@item.LastUpdated?.ToString("HH:mm dd/MM/yyyy")</td>
                        <td>@item.Organization?.OrganizationName</td>
                        <td>@item.ProgramToanNS?.ProgramName</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a asp-page="Edit" asp-route-id="@item.OrganizationProgramTuyenTMID" class="btn btn-warning">Edit</a>
                                <a asp-page="Details" asp-route-id="@item.OrganizationProgramTuyenTMID" class="btn btn-info">Details</a>
                                <a asp-page="Delete" asp-route-id="@item.OrganizationProgramTuyenTMID" class="btn btn-danger">Delete</a>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

@section Scripts {
    <script src="~/js/signalr/dist/browser/signalr.js"></script>
    <script>
        const connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

        connection.start().then(() => console.log("SignalR Connected")).catch(err => console.error(err.toString()));

        function formatDateTime(dateStr) {
            const d = new Date(dateStr);
            return d.toLocaleString('vi-VN', {
                hour: '2-digit', minute: '2-digit',
                day: '2-digit', month: '2-digit', year: 'numeric'
            });
        }
        
        function generateRow(item) {
            console.log(item)
            return `
            <tr id="${item.organizationProgramTuyenTMID}">
                <td>${formatDateTime(item.joinedDate)}</td>
                <td>${item.contributionDescription ?? ""}</td>
                <td><span class="badge ${item.isSponsor ? 'bg-info text-dark' : 'bg-secondary'}">${item.isSponsor ? 'Yes' : 'No'}</span></td>
                <td><span class="badge ${item.isOrganizer ? 'bg-info text-dark' : 'bg-secondary'}">${item.isOrganizer ? 'Yes' : 'No'}</span></td>
                <td>${item.programRole ?? ""}</td>
                <td>${item.fundingAmount?.toLocaleString('vi-VN') ?? "None"} VNĐ</td>
                <td>${item.feedback ?? ""}</td>
                <td>${formatDateTime(item.lastUpdated)}</td>
                <td>${item?.organizationName ?? ""}</td>
                <td>${item?.programName ?? ""}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <a class="btn btn-warning" href="/OrganizationProgramTuyenTM/Edit/${item.organizationProgramTuyenTMID}">Edit</a>
                        <a class="btn btn-info" href="/OrganizationProgramTuyenTM/Details/${item.organizationProgramTuyenTMID}">Details</a>
                        <a class="btn btn-danger" href="/OrganizationProgramTuyenTM/Delete/${item.organizationProgramTuyenTMID}">Delete</a>
                    </div>
                </td>
            </tr>`;
        }

        connection.on("Receiver_DeleteOrganizationTuyenTM", id => {
            $(`#${id}`).remove();
        });

        connection.on("Receiver_CreateOrganizationProgramTuyenTM", item => {
            $('#OrganizationProgramTuyenTMBodyID').append(generateRow(item));
        });

        connection.on("Receiver_UpdateOrganizationProgramTuyenTM", item => {
            $(`#${item.organizationProgramTuyenTMID}`).replaceWith(generateRow(item));
        });
    </script>
}
