﻿@page "/SamplePhuongMts/SamplePhuongMtForm/{SamplePhuongMtID}"
@using DNATesting.Repositories.PhuongMT.Models
@using Microsoft.AspNetCore.Components.Forms
@inject NavigationManager Navigation

<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" />

<style>
    .form-container {
        max-width: 700px;
        margin: 2rem auto;
        padding: 2rem;
        background-color: #ffffff;
        border-radius: 1rem;
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.05);
    }

    .form-title {
        font-size: 1.75rem;
        font-weight: 600;
        color: #1e3a8a;
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

        .form-group label {
            font-weight: 600;
            display: block;
            margin-bottom: 0.4rem;
            color: #374151;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 0.5rem;
            font-size: 1rem;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
        }

    .btn-save {
        background-color: #10b981;
        color: white;
        padding: 0.5rem 1.5rem;
        border: none;
        border-radius: 0.375rem;
        font-size: 1rem;
        transition: background-color 0.2s;
    }

        .btn-save:hover {
            background-color: #059669;
        }

    .btn-cancel {
        background-color: #6b7280;
        color: white;
        margin-left: 1rem;
        padding: 0.5rem 1.2rem;
        border: none;
        border-radius: 0.375rem;
        font-size: 1rem;
        transition: background-color 0.2s;
    }

        .btn-cancel:hover {
            background-color: #4b5563;
        }

    .btn-delete {
        background-color: #ef4444;
        color: white;
        margin-left: 1rem;
        padding: 0.5rem 1.2rem;
        border: none;
        border-radius: 0.375rem;
        font-size: 1rem;
        transition: background-color 0.2s;
    }

        .btn-delete:hover {
            background-color: #dc2626;
        }

    .action-buttons {
        text-align: center;
        margin-top: 2rem;
    }
</style>




<div class="form-container">
    <h3 class="form-title">@((string.IsNullOrEmpty(sample.SamplePhuongMtid) ? "Edit" : "Create") + " Sample")</h3>


    <EditForm FormName="SampleForm" Model="sample" OnValidSubmit="HandleValidSubmit">
        <DataAnnotationsValidator />
        <ValidationSummary />
        <div class="form-group">
            <label>ID</label>
            <InputText class="form-control" @bind-Value="sample.SamplePhuongMtid" />
        </div>
        <div class="form-group">
            <label>Appointment ID</label>
            <InputSelect class="form-control" @bind-Value="sample.AppointmentHanNhmid">
                <option value="">-- Select Appointment --</option>
                @if (appointments != null)
                {
                    foreach (var appt in appointments)
                    {
                        <option value="@appt.AppointmentHanNhmid">
                            @($"{appt.AppointmentHanNhmid} - {appt.ApplicationSource}")
                        </option>
                    }
                }
            </InputSelect>

        </div>

        <div class="form-group">
            <label>Sample Type</label>
            <InputText class="form-control" @bind-Value="sample.SampleType" />
        </div>

        <div class="form-group">
            <label>Belong To</label>
            <InputText class="form-control" @bind-Value="sample.BelongTo" />
        </div>

        <div class="form-group">
            <label>Storage Location</label>
            <InputText class="form-control" @bind-Value="sample.StorageLocation" />
        </div>

        <div class="form-group">
            <label>Status</label>
            <InputText class="form-control" @bind-Value="sample.Status" />
        </div>

        <div class="form-group">
            <label>Storage Temperature (°C)</label>
            <InputNumber class="form-control" @bind-Value="sample.StorageTemperature" />
        </div>

        <div class="form-group">
            <label>Collected By</label>
            <InputText class="form-control" @bind-Value="sample.CollectedBy" />
        </div>

        <div class="form-group">
            <label>Collected Date</label>
            <InputDate class="form-control" @bind-Value="sample.CollectedDate" />
        </div>

        <div class="action-buttons">
            <button type="submit" class="btn-save">
                <i class="fas fa-save"></i> Save
            </button>
            <button type="button" class="btn-cancel" @onclick="Cancel">
                <i class="fas fa-times"></i> Cancel
            </button>
        </div>
    </EditForm>
</div>

@code {
    [Parameter]
    public string? SamplePhuongMtid { get; set; }

    private SamplePhuongMt sample = new();
    private bool isEditMode = false;

    private List<AppointmentHanNhm> appointments = new();

   


    protected override async Task OnInitializedAsync()
    {
        appointments = await serviceProviders.appointmentHanNHMServices.GetAllAsync();

        if (!string.IsNullOrEmpty(SamplePhuongMtid))
        {
            var existing = await serviceProviders.samplePhuongMTService.GetByIdAsync(SamplePhuongMtid);
            if (existing != null)
            {
                sample = existing;
            }
            else
            {
                sample.SamplePhuongMtid = SamplePhuongMtid; // giữ nguyên để user điền
            }
        }
    }

    
    
    private async Task HandleValidSubmit()
    {
        var existing = await serviceProviders.samplePhuongMTService.GetByIdAsync(sample.SamplePhuongMtid ?? "");

        if (existing != null)
        {
            // Record exists -> update
            await serviceProviders.samplePhuongMTService.UpdateAsync(sample);
        }
        else
        {
            // Record does not exist -> create
            if (string.IsNullOrEmpty(sample.SamplePhuongMtid))
            {
                sample.SamplePhuongMtid = Guid.NewGuid().ToString();
            }

            await serviceProviders.samplePhuongMTService.CreateAsync(sample);
        }

        Navigation.NavigateTo("/SamplePhuongMts/SamplePhuongMtList");
    }

    private void Cancel()
    {
        Navigation.NavigateTo("/SamplePhuongMts/SamplePhuongMtList");
    }
}
