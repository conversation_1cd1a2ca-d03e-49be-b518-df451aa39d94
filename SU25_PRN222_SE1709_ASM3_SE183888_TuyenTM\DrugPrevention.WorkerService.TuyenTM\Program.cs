﻿using DrugPrevention.Services.TuyenTM;
using DrugPrevention.WorkerService.TuyenTM;

var builder = Host.CreateApplicationBuilder(args);
builder.Services.AddHostedService<Worker>();
//thêm Workder Service
builder.Services.AddWindowsService(options =>
{
    options.ServiceName = "TuyenTM_WorkerService";    // as the same name in: sc create "" binpath=""    
});
//thêm IServiceProviders và ServiceProviders của Service
builder.Services.AddSingleton<IServiceProviders, ServiceProviders>();
builder.Services.AddSingleton<IOrganizationProgramsTuyenTMService, OrganizationProgramsTuyenTMService>();

var host = builder.Build();
host.Run();
