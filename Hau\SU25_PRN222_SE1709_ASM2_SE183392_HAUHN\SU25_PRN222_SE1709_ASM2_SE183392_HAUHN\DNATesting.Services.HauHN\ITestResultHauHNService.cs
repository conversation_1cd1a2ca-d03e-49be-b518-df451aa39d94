﻿using DNATesting.Repositories.HauHN.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.HauHN
{
    public interface ITestResultHauHNService
    {
        Task<List<TestResultHauHn>> GetAllAsync();
        Task<TestResultHauHn> GetIdAsync(String id);
        Task<List<TestResultHauHn>> SearchAsync(string code, bool? isVerified, string apID);
        Task<int> CreateAsync(TestResultHauHn result);
        Task<int> UpdateAsync(TestResultHauHn result);
        Task<bool> DeleteAsync(string result);
        Task<bool> TestResultHauHnExistsAsync(String id);
    }
}
