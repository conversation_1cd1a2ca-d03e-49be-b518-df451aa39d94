﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DNATesting.Repositories.PhuongMT.Models;

public partial class DnatestOptionKhanhHn
{
    public string DnatestOptionKhanhHnid { get; set; }

    public string OptionName { get; set; }

    public string OptionType { get; set; }

    public decimal? Price { get; set; }

    public bool? IsCivilType { get; set; }

    public string Description { get; set; }

    public double? EstimatedProcessingTime { get; set; }

    public string RecommendedUse { get; set; }

    public bool? RequiresKitDelivery { get; set; }

    public bool? SupportsHomeCollection { get; set; }

    public bool? IsActive { get; set; }

    public DateTime? CreatedAt { get; set; }

    public int? CreatBy { get; set; }

    public DateTime? LastUpdated { get; set; }

    public int? UpdateBy { get; set; }

    public virtual SystemUserAccount CreatByNavigation { get; set; }

    public virtual ICollection<DnatestOptionDetailKhanhHn> DnatestOptionDetailKhanhHns { get; set; } = new List<DnatestOptionDetailKhanhHn>();

    public virtual ICollection<FeedbackHoangDn> FeedbackHoangDns { get; set; } = new List<FeedbackHoangDn>();

    public virtual ICollection<ReportDetailTinPt> ReportDetailTinPts { get; set; } = new List<ReportDetailTinPt>();

    public virtual SystemUserAccount UpdateByNavigation { get; set; }

    public virtual ICollection<AppointmentHanNhm> AppointmentHanNhms { get; set; } = new List<AppointmentHanNhm>();
}