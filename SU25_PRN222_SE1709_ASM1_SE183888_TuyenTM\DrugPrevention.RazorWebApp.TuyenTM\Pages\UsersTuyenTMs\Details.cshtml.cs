﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using DrugPrevention.Repositories.TuyenTM.DBContext;
using DrugPrevention.Repositories.TuyenTM.Models;
using DrugPrevention.Services.TuyenTM;
using Microsoft.AspNetCore.Authorization;

namespace DrugPrevention.RazorWebApp.TuyenTM.Pages.UsersTuyenTMs
{
    [Authorize(Roles = "1,2")]
    public class DetailsModel : PageModel
    {
        //private readonly DrugPrevention.Repositories.TuyenTM.DBContext.SU25_PRN222_SE1709_G2_DrugPreventionSystemContext _context;
        private readonly IUsersTuyenTMService _usersTuyenTMService;

        public DetailsModel(IUsersTuyenTMService usersTuyenTMService)
        {
            _usersTuyenTMService = usersTuyenTMService;
        }

        [BindProperty]
        public UsersTuyenTM UsersTuyenTM { get; set; } = default!;

        public async Task<IActionResult> OnGetAsync(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var userstuyentm = await _usersTuyenTMService.GetByIdAsync(id.Value);
            if (userstuyentm == null)
            {
                return NotFound();
            }

            UsersTuyenTM = userstuyentm;
            
            return Page();
        }
    }
}
