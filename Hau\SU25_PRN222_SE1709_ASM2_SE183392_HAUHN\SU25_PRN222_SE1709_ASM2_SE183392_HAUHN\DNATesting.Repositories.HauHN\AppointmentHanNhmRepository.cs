﻿using DNATesting.Repositories.HauHN.Basic;
using DNATesting.Repositories.HauHN.DBContext;
using DNATesting.Repositories.HauHN.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Repositories.HauHN
{
    public class AppointmentHanNhmRepository : GenericRepository<AppointmentHanNhm>
    {
        public AppointmentHanNhmRepository() { }
        public AppointmentHanNhmRepository(SE18_PRN222_SE1709_G3_DNA_TestingContext context) => _context = context;
    }
}
