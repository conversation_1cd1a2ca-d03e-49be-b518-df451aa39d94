﻿using DNATesting.Repositories.PhuongMT.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Repositories.PhuongMT
{
    public interface IUnitOfWork : IDisposable
    {
        SystemUserAccountRepository SystemUserAccountRepository { get; }
        SamplePhuongMTRepository SamplePhuongMTRepository { get; }
        TestParticipantsHauHnRepository TestParticipantsHauHnRepository { get; }
        AppointmentHanNHMRepository AppointmentHanNHMRepository { get; }

        int SaveChangesWithTransaction();
        Task<int> SaveChangesWithTransactionAsync();
    }
    public class UnitOfWork : IUnitOfWork
    {

        private readonly SE18_PRN222_SE1709_G3_DNA_TestingContext _context;
        private SystemUserAccountRepository _userAccountRepository;
        private AppointmentHanNHMRepository _appointmentHanNHMRepository;
        private SamplePhuongMTRepository _samplePhuongMTRepository;
        private TestParticipantsHauHnRepository _testParticipantsHauHnRepository;

        public UnitOfWork() => _context = new SE18_PRN222_SE1709_G3_DNA_TestingContext();
        public SystemUserAccountRepository SystemUserAccountRepository
        {
            get
            {
                return _userAccountRepository ??= new SystemUserAccountRepository(_context);
            }
        }

        public SamplePhuongMTRepository SamplePhuongMTRepository
        {
            get
            {
                return _samplePhuongMTRepository ??= new SamplePhuongMTRepository(_context);
            }
        }

        public TestParticipantsHauHnRepository TestParticipantsHauHnRepository
        {
            get
            {
                return _testParticipantsHauHnRepository??= new TestParticipantsHauHnRepository(_context);
            }
        }

        public AppointmentHanNHMRepository AppointmentHanNHMRepository
        {
            get
            {
                return _appointmentHanNHMRepository ??= new AppointmentHanNHMRepository(_context);
            }
        }


        public void Dispose() => _context.Dispose();

        public int SaveChangesWithTransaction()
        {
            int result = -1;

            //System.Data.IsolationLevel.Snapshot
            using (var dbContextTransaction = _context.Database.BeginTransaction())
            {
                try
                {
                    result = _context.SaveChanges();
                    dbContextTransaction.Commit();
                }
                catch (Exception)
                {
                    //Log Exception Handling message                      
                    result = -1;
                    dbContextTransaction.Rollback();
                }
            }

            return result;
        }

        public async Task<int> SaveChangesWithTransactionAsync()
        {
            int result = -1;

            //System.Data.IsolationLevel.Snapshot
            using (var dbContextTransaction = _context.Database.BeginTransaction())
            {
                try
                {
                    result = await _context.SaveChangesAsync();
                    dbContextTransaction.Commit();
                }
                catch (Exception)
                {
                    //Log Exception Handling message                      
                    result = -1;
                    dbContextTransaction.Rollback();
                }
            }

            return result;
        }
    }


}
