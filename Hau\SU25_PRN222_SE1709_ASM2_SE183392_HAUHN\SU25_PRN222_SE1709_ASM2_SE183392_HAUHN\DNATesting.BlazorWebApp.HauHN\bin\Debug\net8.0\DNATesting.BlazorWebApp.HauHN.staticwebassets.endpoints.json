{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "DNATesting.BlazorWebApp.HauHN.g3uftwdgky.styles.css", "AssetFile": "DNATesting.BlazorWebApp.HauHN.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5941"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 10:23:27 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g3uftwdgky"}, {"Name": "integrity", "Value": "sha256-HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs="}, {"Name": "label", "Value": "DNATesting.BlazorWebApp.HauHN.styles.css"}]}, {"Route": "DNATesting.BlazorWebApp.HauHN.styles.css", "AssetFile": "DNATesting.BlazorWebApp.HauHN.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5941"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 10:23:27 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HNNK6wh8USZ4JQRQ7qOOQXrtddZ6O3QTVN34x+T9hOs="}]}, {"Route": "app.73zgofhkbc.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "3522"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XSalpy5Yk5nPT8Ff/GQ6OMLaE2GUDC4N26XNyB3J66U=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 15:07:01 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "73zgofhkbc"}, {"Name": "integrity", "Value": "sha256-XSalpy5Yk5nPT8Ff/GQ6OMLaE2GUDC4N26XNyB3J66U="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "3522"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XSalpy5Yk5nPT8Ff/GQ6OMLaE2GUDC4N26XNyB3J66U=\""}, {"Name": "Last-Modified", "Value": "Sun, 15 Jun 2025 15:07:01 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XSalpy5Yk5nPT8Ff/GQ6OMLaE2GUDC4N26XNyB3J66U="}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}, {"Name": "label", "Value": "favicon.png"}]}, {"Route": "favicon.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:21 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "images/original-8e7ce77dec0edaf0105e8287038f6e60.e1p5ytybew.gif", "AssetFile": "images/original-8e7ce77dec0edaf0105e8287038f6e60.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2328104"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"/hslHbN19UnlVHbs+JX1dVEbqXa46bFwG6GMKmJy/Sc=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 09:38:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e1p5ytybew"}, {"Name": "integrity", "Value": "sha256-/hslHbN19UnlVHbs+JX1dVEbqXa46bFwG6GMKmJy/Sc="}, {"Name": "label", "Value": "images/original-8e7ce77dec0edaf0105e8287038f6e60.gif"}]}, {"Route": "images/original-8e7ce77dec0edaf0105e8287038f6e60.gif", "AssetFile": "images/original-8e7ce77dec0edaf0105e8287038f6e60.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2328104"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"/hslHbN19UnlVHbs+JX1dVEbqXa46bFwG6GMKmJy/Sc=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 09:38:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/hslHbN19UnlVHbs+JX1dVEbqXa46bFwG6GMKmJy/Sc="}]}]}