﻿@* <Router AppAssembly="typeof(Program).Assembly">
    <Found Context="routeData">
        <RouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)" />
        <FocusOnNavigate RouteData="routeData" Selector="h1" />
    </Found>
</Router> *@

<Router AppAssembly="@typeof(Program).Assembly">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(Layout.MainLayout)">
            <NotAuthorized>
                    <div class="container mt-5">
                        <div class="alert alert-danger text-center">
                            <h4 class="mb-3">🚫 Access Denied</h4>
                            <p> You do not have permission to access this page. </p>
                            <a href="/" class="btn btn-primary mt-3">Go back</a>
                        </div>
                    </div>
            </NotAuthorized>
        </AuthorizeRouteView>
    </Found>
    <NotFound>
        <LayoutView Layout="@typeof(Layout.MainLayout)">
            <p class="text-danger">Not Found</p>
        </LayoutView>
    </NotFound>
</Router>