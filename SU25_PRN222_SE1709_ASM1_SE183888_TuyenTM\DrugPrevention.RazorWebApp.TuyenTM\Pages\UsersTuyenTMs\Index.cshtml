﻿@page
@model DrugPrevention.RazorWebApp.TuyenTM.Pages.UsersTuyenTMs.IndexModel

@{
    ViewData["Title"] = "User Management";
}

<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="text-primary">User Management</h2>
        <a class="btn btn-success" asp-page="Create">
            <i class="bi bi-plus-circle"></i> Create New User
        </a>
    </div>

    <div class="card shadow-sm">
        <div class="card-body">
            <table class="table table-hover table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>@Html.DisplayNameFor(model => model.UsersTuyenTM[0].Username)</th>
                        <th>@Html.DisplayNameFor(model => model.UsersTuyenTM[0].Email)</th>
                        <th>@Html.DisplayNameFor(model => model.UsersTuyenTM[0].FirstName)</th>
                        <th>@Html.DisplayNameFor(model => model.UsersTuyenTM[0].LastName)</th>
                        <th>@Html.DisplayNameFor(model => model.UsersTuyenTM[0].Role)</th>
                        <th>@Html.DisplayNameFor(model => model.UsersTuyenTM[0].RegistrationDate)</th>
                        <th>@Html.DisplayNameFor(model => model.UsersTuyenTM[0].IsActive)</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="UserTuyenTMBodyID">
                    @foreach (var item in Model.UsersTuyenTM)
                    {
                        <tr id="@item.UserTuyenTMID">
                            <td>@item.Username</td>
                            <td>@item.Email</td>
                            <td>@item.FirstName</td>
                            <td>@item.LastName</td>
                            <td>@item.Role</td>
                            <td>@item.RegistrationDate.ToString("dd-MM-yyyy")</td>
                            <td>
                                @if (item.IsActive)
                                {
                                    <span class="badge bg-success">Active</span>
                                }
                                else
                                {
                                    <span class="badge bg-secondary">Inactive</span>
                                }
                            </td>
                            <td>
                                <a class="btn btn-sm btn-outline-primary me-1" asp-page="./Edit" asp-route-id="@item.UserTuyenTMID">
                                    <i class="bi bi-pencil"></i> Edit
                                </a>
                                <a class="btn btn-sm btn-outline-info me-1" asp-page="./Details" asp-route-id="@item.UserTuyenTMID">
                                    <i class="bi bi-eye"></i> Details
                                </a>
                                <a class="btn btn-sm btn-outline-danger" asp-page="./Delete" asp-route-id="@item.UserTuyenTMID">
                                    <i class="bi bi-trash"></i> Delete
                                </a>
                            </td>

                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>
<script src="~/js/signalr/\dist/\browser/signalr.js"></script>
<script>
    "use strict";

    var connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

    connection.start().then(function () {
        console.log("Connected to the SignalR Hub");
    }).catch(function (err) {
        return console.error(err.toString());
    });

    //Delete
    connection.on("Receiver_DeleteUserTuyenTM", function (UserTuyenTMID) {
        alert($(`#${UserTuyenTMID}`).text());
        $('#UserTuyenTMBodyID').find(`tr[id='${UserTuyenTMID}']`).remove();
    });

    //Create
        connection.on("Receiver_CreateUserTuyenTM", function (item) {
        //alert('Receiver_CreateTransactionCashDepositSlip');
        console.log(item);
        var trTransactionCashDepositSlip = `
                <tr>
                    
                    <td>${item.username}</td>
                    <td>${item.email}</td>
                    <td>${item.firstName}</td>
                    <td>${item.lastName}</td>
                    <td>${item.role}</td>
                    <td>${item.registrationDate}</td>
                    <td>
                        <span class="badge bg-secondary">Inactive</span>    
                    </td>
                    <td>
                        <a class="btn btn-sm btn-outline-primary me-1" asp-page="./Edit" asp-route-id="${item.UserTuyenTMID}">
                            <i class="bi bi-pencil"></i> Edit
                        </a>
                        <a class="btn btn-sm btn-outline-info me-1" asp-page="./Details" asp-route-id="${item.UserTuyenTMID}">
                            <i class="bi bi-eye"></i> Details
                        </a>
                        <a class="btn btn-sm btn-outline-danger" asp-page="./Delete" asp-route-id="${item.UserTuyenTMID}">
                            <i class="bi bi-trash"></i> Delete
                        </a>
                    </td>
                </tr>
            `;

        alert(trTransactionCashDepositSlip);

        $('#UserTuyenTMBodyID').append(trTransactionCashDepositSlip)
    });

    //Update
        connection.on("Receiver_UpdateUserTuyenTM", function (item) {
        var row = $(`#${item.userTuyenTMID}`);
        if (row.length > 0) {
            var statusBadge = item.isActive
                ? '<span class="badge bg-success">Active</span>'
                : '<span class="badge bg-secondary">Inactive</span>';

            var newRowHtml = `
                <td>${item.username}</td>
                <td>${item.email}</td>
                <td>${item.firstName}</td>
                <td>${item.lastName}</td>
                <td>${item.role}</td>
                <td>${new Date(item.registrationDate).toLocaleDateString('en-GB')}</td>
                <td>${statusBadge}</td>
                <td>
                    <a class="btn btn-sm btn-outline-primary me-1" href="/TuyenTM/UsersTuyenTMs/Edit?id=${item.userTuyenTMID}">
                        <i class="bi bi-pencil"></i> Edit
                    </a>
                    <a class="btn btn-sm btn-outline-info me-1" href="/TuyenTM/UsersTuyenTMs/Details?id=${item.userTuyenTMID}">
                        <i class="bi bi-eye"></i> Details
                    </a>
                    <a class="btn btn-sm btn-outline-danger" href="/TuyenTM/UsersTuyenTMs/Delete?id=${item.userTuyenTMID}">
                        <i class="bi bi-trash"></i> Delete
                    </a>
                </td>
            `;
            row.html(newRowHtml);
        }
    });

        
</script>
