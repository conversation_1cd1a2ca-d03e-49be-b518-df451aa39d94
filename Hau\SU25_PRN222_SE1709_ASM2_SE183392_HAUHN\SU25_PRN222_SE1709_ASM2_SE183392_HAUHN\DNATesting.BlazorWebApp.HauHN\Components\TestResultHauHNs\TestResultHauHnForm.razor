﻿@page "/TestResultHauHNs/TestResultHauHnForm/{ResultHauHnid?}"
@using DNATesting.Repositories.HauHN.Models
@using Microsoft.AspNetCore.Authorization
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

@attribute [Authorize(Roles = "1")]
<h3>@(string.IsNullOrEmpty(ResultHauHnid) ? "Create New Test Result" : "Edit Test Result")</h3>

@if (isLoading)
{
    <div class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">Loading test results...</p>
    </div>
}
else
{
    <div class="row">
        <div class="col-md-8">
            <EditForm Model="@testResultHauHn" OnValidSubmit="async() => await HandleValidSubmit()" FormName="testResultForm">
                <DataAnnotationsValidator />
                <ValidationSummary />

                @if (string.IsNullOrEmpty(ResultHauHnid))
                {
                    <div class="form-group mb-3">
                        <label for="resultHauHnId">Result ID</label>
                        <InputText id="resultHauHnId" @bind-Value="testResultHauHn.ResultHauHnid" class="form-control" />
                    </div>
                }
                else
                {
                    <div class="form-group mb-3">
                        <label for="resultHauHnId">Result ID</label>
                        <InputText id="resultHauHnId" @bind-Value="testResultHauHn.ResultHauHnid" class="form-control" disabled />
                    </div>
                }

                <div class="form-group mb-3">
                    <label for="appointmentId">Appointment ID</label>
                    <InputSelect id="appointmentId" @bind-Value="testResultHauHn.AppointmentHanNhmid" class="form-control">
                        <option value="">Select an appointment</option>
                        @foreach (var appointment in appointments)
                        {
                            <option value="@appointment.AppointmentHanNhmid">@appointment.AppointmentHanNhmid</option>
                        }
                    </InputSelect>
                    <ValidationMessage For="@(() => testResultHauHn.AppointmentHanNhmid)" />
                </div>

                <div class="form-group mb-3">
                    <label for="testType">Test Type</label>
                    <InputText id="testType" @bind-Value="testResultHauHn.TestType" class="form-control" />
                    <ValidationMessage For="@(() => testResultHauHn.TestType)" />
                </div>

                <div class="form-group mb-3">
                    <label for="resultValue">Result Value</label>
                    <InputText id="resultValue" @bind-Value="testResultHauHn.ResultValue" class="form-control" />
                    <ValidationMessage For="@(() => testResultHauHn.ResultValue)" />
                </div>

                <div class="form-group mb-3">
                    <label for="resultDate">Result Date</label>
                    <InputDate id="resultDate" @bind-Value="testResultHauHn.ResultDate" class="form-control" />
                    <ValidationMessage For="@(() => testResultHauHn.ResultDate)" />
                </div>

                <div class="form-group mb-3">
                    <label for="testDate">Test Date</label>
                    <InputDate id="testDate" @bind-Value="testResultHauHn.TestDate" class="form-control" />
                    <ValidationMessage For="@(() => testResultHauHn.TestDate)" />
                </div>

                <div class="form-group mb-3">
                    <label for="technicianId">Technician ID</label>
                    <InputSelect id="technicianId" @bind-Value="testResultHauHn.TechnicianId" class="form-control">
                        <option value="">Select a technician</option>
                        @foreach (var user in users)
                        {
                            <option value="@user.UserAccountId">@user.UserAccountId - @user.UserName</option>
                        }
                    </InputSelect>
                    <ValidationMessage For="@(() => testResultHauHn.TechnicianId)" />
                </div>

                <div class="form-group mb-3">
                    <label for="isVerified">Is Verified</label>
                    <div class="form-check">
                        <InputCheckbox id="isVerified" @bind-Value="testResultHauHn.IsVerified" class="form-check-input" />
                        <label class="form-check-label" for="isVerified">Verified</label>
                    </div>
                    <ValidationMessage For="@(() => testResultHauHn.IsVerified)" />
                </div>

                <div class="form-group mb-3">
                    <label for="verifiedBy">Verified By</label>
                    <InputSelect id="verifiedBy" @bind-Value="testResultHauHn.VerifiedBy" class="form-control">
                        <option value="">Select a verifier</option>
                        @foreach (var user in users)
                        {
                            <option value="@user.UserAccountId">@user.UserAccountId - @user.UserName</option>
                        }
                    </InputSelect>
                    <ValidationMessage For="@(() => testResultHauHn.VerifiedBy)" />
                </div>

                <div class="form-group mb-3">
                    <label for="geneticMutation">Genetic Mutation</label>
                    <InputTextArea id="geneticMutation" @bind-Value="testResultHauHn.GeneticMutation" class="form-control" />
                    <ValidationMessage For="@(() => testResultHauHn.GeneticMutation)" />
                </div>

                <div class="form-group mb-3">
                    <label for="diseaseRisk">Disease Risk</label>
                    <InputNumber id="diseaseRisk" @bind-Value="testResultHauHn.DiseaseRisk" class="form-control" step="0.01" />
                    <ValidationMessage For="@(() => testResultHauHn.DiseaseRisk)" />
                </div>

                <div class="form-group mb-3">
                    <label for="relationshipProbability">Relationship Probability</label>
                    <InputNumber id="relationshipProbability" @bind-Value="testResultHauHn.RelationshipProbability" class="form-control" step="0.01" />
                    <ValidationMessage For="@(() => testResultHauHn.RelationshipProbability)" />
                </div>
                <div class="mt-3 d-flex gap-2">
                    <button type="button" class="btn btn-danger" onclick="@(async () => await DeleteTestResultHauhn(ResultHauHnid))">Delete</button>
                    <button type="submit" class="btn btn-primary">@(string.IsNullOrEmpty(ResultHauHnid) ? "Create" : "Save")</button>
                    <a href="/TestResultHauHNs/TestResultHauHnList" class="btn btn-secondary">Cancel</a>
                </div>
            </EditForm>
        </div>
    </div>
}

@code {
    [Parameter]
    public string ResultHauHnid { get; set; }

    private TestResultHauHn testResultHauHn = new TestResultHauHn();


    private bool isLoading = true;
    private List<AppointmentHanNhm> appointments = new List<AppointmentHanNhm>();
    private List<SystemUserAccount> users = new List<SystemUserAccount>();

    protected override async Task OnInitializedAsync()
    {
        try
        {

            // Load appointments and users
            appointments = await ServiceProviders.appointmentHanNhmService.GetAllAsync();
            users = await ServiceProviders.systemUserAccountService.GetAllUserAccountAsync();
            if (!string.IsNullOrEmpty(ResultHauHnid))
            {
                testResultHauHn = await ServiceProviders.testResultHauHNService.GetIdAsync(ResultHauHnid);
                if (testResultHauHn == null)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Test result not found!");
                    NavigationManager.NavigateTo("/TestResultHauHNs/TestResultHauHnList");
                    return;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading data: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", "Error loading data. Please try again.");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task HandleValidSubmit()
    {
        var result = 0;
        var testResultExist = await ServiceProviders.testResultHauHNService.GetIdAsync(testResultHauHn.ResultHauHnid);
        if (testResultExist != null && testResultExist.ResultHauHnid != null)
        {
            result = await ServiceProviders.testResultHauHNService.UpdateAsync(testResultHauHn);
            if (result > 0)
            {
                NavigationManager.NavigateTo("/TestResultHauHNs/TestResultHauHnList", true);
            }
        }
        else
        {
            result = await ServiceProviders.testResultHauHNService.CreateAsync(testResultHauHn);
            if (result > 0)
            {
                NavigationManager.NavigateTo("/TestResultHauHNs/TestResultHauHnList", true);
            }
        }
    }
    private async Task DeleteTestResultHauhn(string resultHauHnid)
    {
        try
        {
            if (!string.IsNullOrEmpty(resultHauHnid))
            {
                var confirm = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this test result?");
                if (confirm)
                {
                    // Check if the test result exists
                    var existingResult = await ServiceProviders.testResultHauHNService.GetIdAsync(resultHauHnid);
                    if (existingResult == null)
                    {
                        await JSRuntime.InvokeVoidAsync("alert", "Test result not found!");
                        NavigationManager.NavigateTo("/TestResultHauHNs/TestResultHauHnList", true);
                        return;
                    }

                    // Try to delete
                    var result = await ServiceProviders.testResultHauHNService.DeleteAsync(resultHauHnid);
                    if (result)
                    {
                        await JSRuntime.InvokeVoidAsync("alert", "Test result deleted successfully!");
                        // Force a full page reload to clear any cached state
                        NavigationManager.NavigateTo("/TestResultHauHNs/TestResultHauHnList", true);
                    }
                    else
                    {
                        await JSRuntime.InvokeVoidAsync("alert", "Cannot delete this test result. It may be referenced by other records.");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deleting test result: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", "Error deleting test result. Please try again.");
            // Force a full page reload on error
            NavigationManager.NavigateTo("/TestResultHauHNs/TestResultHauHnList", true);
        }
    }
}
