-- Insert test users into System_UserAccounts table
-- Make sure to run this script on your database: SU25_PRN222_SE1709_G2_DrugPreventionSystem

USE [SU25_PRN222_SE1709_G2_DrugPreventionSystem]
GO

-- Insert test users
INSERT INTO [dbo].[System_UserAccounts] 
    ([UserName], [Password], [FullName], [Email], [Phone], [EmployeeCode], [RoleId], [RequestCode], [CreatedDate], [ApplicationCode], [CreatedBy], [ModifiedDate], [ModifiedBy], [IsActive])
VALUES 
    ('admin', '123456', 'Administrator', '<EMAIL>', '**********', 'EMP001', 1, 'REQ001', GETDATE(), 'APP001', 'system', GETDATE(), 'system', 1),
    ('manager', '123456', 'Manager User', '<EMAIL>', '**********', 'EMP002', 2, 'REQ002', GETDATE(), 'APP001', 'system', GETDATE(), 'system', 1),
    ('staff', '123456', 'Staff User', '<EMAIL>', '**********', 'EMP003', 3, 'REQ003', GETDATE(), 'APP001', 'system', GETDATE(), 'system', 1),
    ('user', '123456', 'Regular User', '<EMAIL>', '**********', 'EMP004', 4, 'REQ004', GETDATE(), 'APP001', 'system', GETDATE(), 'system', 1);

GO

-- Verify the inserted data
SELECT * FROM [dbo].[System_UserAccounts] WHERE [IsActive] = 1;
