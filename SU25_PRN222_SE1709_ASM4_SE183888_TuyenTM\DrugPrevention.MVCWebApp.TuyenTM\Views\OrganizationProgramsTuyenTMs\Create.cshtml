﻿@model DrugPrevention.Repositories.TuyenTM.Models.OrganizationProgramsTuyenTM

@{
    ViewData["Title"] = "Create Organization Program";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-plus-circle me-2"></i>Create New Organization Program
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="Create">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="OrganizationID" class="form-label">
                                        <i class="fas fa-building mr-1"></i>Organization
                                    </label>
                                    <select asp-for="OrganizationID" class="form-control" asp-items="ViewBag.OrganizationID">
                                        <option value="">-- Select Organization --</option>
                                    </select>
                                    <span asp-validation-for="OrganizationID" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="ProgramToanNSID" class="form-label">
                                        <i class="fas fa-project-diagram mr-1"></i>Program
                                    </label>
                                    <select asp-for="ProgramToanNSID" class="form-control" asp-items="ViewBag.ProgramToanNSID">
                                        <option value="">-- Select Program --</option>
                                    </select>
                                    <span asp-validation-for="ProgramToanNSID" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label asp-for="ProgramRole" class="form-label">
                                        <i class="fas fa-user-tag mr-1"></i>Program Role
                                    </label>
                                    <input asp-for="ProgramRole" class="form-control" placeholder="Enter role in program" />
                                    <span asp-validation-for="ProgramRole" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="ContributionDescription" class="form-label">
                                <i class="fas fa-comment mr-1"></i>Contribution Description
                            </label>
                            <textarea asp-for="ContributionDescription" class="form-control" rows="3"
                                      placeholder="Describe the contribution to this program..."></textarea>
                            <span asp-validation-for="ContributionDescription" class="text-danger"></span>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="IsSponsor" class="form-label">
                                        <i class="fas fa-dollar-sign mr-1"></i>Is Sponsor
                                    </label>
                                    <select asp-for="IsSponsor" class="form-control">
                                        <option value="">-- Select --</option>
                                        <option value="true">Yes</option>
                                        <option value="false">No</option>
                                    </select>
                                    <span asp-validation-for="IsSponsor" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="IsOrganizer" class="form-label">
                                        <i class="fas fa-users mr-1"></i>Is Organizer
                                    </label>
                                    <select asp-for="IsOrganizer" class="form-control">
                                        <option value="">-- Select --</option>
                                        <option value="true">Yes</option>
                                        <option value="false">No</option>
                                    </select>
                                    <span asp-validation-for="IsOrganizer" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label asp-for="FundingAmount" class="form-label">
                                        <i class="fas fa-money-bill mr-1"></i>Funding Amount
                                    </label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text">$</span>
                                        </div>
                                        <input asp-for="FundingAmount" class="form-control" type="number" step="0.01" min="0" placeholder="0.00" />
                                    </div>
                                    <span asp-validation-for="FundingAmount" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label asp-for="Feedback" class="form-label">
                                <i class="fas fa-star mr-1"></i>Feedback
                            </label>
                            <textarea asp-for="Feedback" class="form-control" rows="3"
                                      placeholder="Enter feedback about this program participation..."></textarea>
                            <span asp-validation-for="Feedback" class="text-danger"></span>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left mr-1"></i>Back to List
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-save mr-1"></i>Create Program
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
