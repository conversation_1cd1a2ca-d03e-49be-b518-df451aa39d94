-- Insert users
INSERT INTO UsersTuyenTM (<PERSON><PERSON><PERSON>, Password, <PERSON>ail, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Role, RegistrationDate, IsActive, PhoneNumber)
VALUES
    ('admin', 'hashed_password_1', '<EMAIL>', 'Admin', 'User', 'Admin', '2024-05-01', 1, '1234567890'),
    ('manager1', 'hashed_password_2', '<EMAIL>', '<PERSON>', '<PERSON>', 'Manager', '2024-05-02', 1, '2234567890'),
    ('consultant1', 'hashed_password_3', '<EMAIL>', '<PERSON>', '<PERSON>', 'Consultant', '2024-05-03', 1, '3234567890'),
    ('consultant2', 'hashed_password_4', '<EMAIL>', '<PERSON>', '<PERSON>', 'Consultant', '2024-05-03', 1, '4234567890'),
    ('staff1', 'hashed_password_5', '<EMAIL>', '<PERSON>', '<PERSON>', 'Staff', '2024-05-04', 1, '5234567890'),
    ('member1', 'hashed_password_6', '<EMAIL>', '<PERSON>', '<PERSON>', 'Member', '2024-05-05', 1, '6234567890'),
    ('member2', 'hashed_password_7', '<EMAIL>', '<PERSON>', '<PERSON>', 'Member', '2024-05-06', 1, '7234567890'),
    ('guest1', 'hashed_password_8', '<EMAIL>', 'David', '<PERSON>', 'Guest', '2024-05-07', 1, '8234567890'),
    ('consultant3', 'hashed_password_9', '<EMAIL>', 'Emily', 'Anderson', 'Consultant', '2024-05-08', 1, '9234567890'),
    ('member3', 'hashed_password_10', '<EMAIL>', 'Daniel', 'Thomas', 'Member', '2024-05-09', 1, '0234567890');
GO

-- Insert courses
INSERT INTO CoursesQuangTNV (Title, AgeGroup, Category, DurationInMinutes, InstructorName, ContentURL, CreatedDate, IsActive, Rating)
VALUES
    ('Understanding Drug Addiction', 'Adults', 'Education', 120, 'Dr. Robert Johnson', '/courses/drug-addiction', '2024-01-15', 1, 4.5),
    ('Substance Abuse Prevention for Teens', 'Teenagers', 'Prevention', 90, 'Lisa Martinez, PhD', '/courses/teen-prevention', '2024-01-20', 1, 4.8),
    ('Drug Effects on the Brain', 'Adults', 'Education', 150, 'Dr. Michael Chen', '/courses/brain-effects', '2024-02-01', 1, 4.3),
    ('Early Signs of Addiction', 'Parents', 'Awareness', 60, 'Susan Wilson, MSW', '/courses/early-signs', '2024-02-10', 1, 4.7),
    ('Healthy Coping Mechanisms', 'All Ages', 'Well-being', 75, 'James Thompson, LPC', '/courses/coping', '2024-02-15', 1, 4.6),
    ('Drug Prevention Strategies for Schools', 'Educators', 'Training', 180, 'Dr. Elizabeth Brown', '/courses/school-strategies', '2024-03-01', 1, 4.4),
    ('Recovery Support Systems', 'Adults', 'Recovery', 120, 'Mark Davis, CADC', '/courses/recovery-support', '2024-03-10', 1, 4.9),
    ('Talking to Kids About Drugs', 'Parents', 'Communication', 90, 'Patricia Garcia, PhD', '/courses/parent-talks', '2024-03-20', 1, 4.2),
    ('Peer Pressure Resistance', 'Teenagers', 'Skills', 60, 'Thomas Miller, EdD', '/courses/peer-pressure', '2024-04-01', 1, 4.7),
    ('Substance Abuse in the Workplace', 'Adults', 'Professional', 120, 'Laura Wilson, MBA', '/courses/workplace', '2024-04-15', 1, 4.5);
GO

-- Insert surveys
INSERT INTO SurveysNamND (SurveyName, SurveyType, TotalQuestions, MaxRiskScore, TargetAudience, IsActive, IsAnonymous, CreatedDate, Creator)
VALUES
    ('Drug Use Risk Assessment', 'Risk Assessment', 15, 100, 'Adults', 1, 1, '2024-01-05', 1),
    ('Teen Substance Awareness', 'Awareness', 10, 80, 'Teenagers', 1, 1, '2024-01-10', 2),
    ('Post-Recovery Evaluation', 'Evaluation', 12, 90, 'Adults', 1, 0, '2024-01-20', 1),
    ('Parent Knowledge Assessment', 'Knowledge', 15, NULL, 'Parents', 1, 0, '2024-02-05', 3),
    ('School Environment Survey', 'Environment', 20, 100, 'Educators', 1, 1, '2024-02-15', 2),
    ('Community Needs Assessment', 'Needs Assessment', 18, NULL, 'Community', 1, 1, '2024-03-01', 1),
    ('Treatment Satisfaction Survey', 'Satisfaction', 10, NULL, 'Patients', 1, 0, '2024-03-15', 4),
    ('Peer Influence Assessment', 'Social', 12, 80, 'Teenagers', 1, 1, '2024-04-01', 3),
    ('Workplace Drug Policy Survey', 'Policy', 15, NULL, 'Employees', 1, 0, '2024-04-10', 2),
    ('Prevention Program Feedback', 'Feedback', 10, NULL, 'All Ages', 1, 0, '2024-04-20', 1);

GO

-- Insert consultants
INSERT INTO ConsultantsTrongLH (UserID, Specialization, Qualification, Certification, YearsOfExperience, AverageRating, TotalConsultations, IsAvailable, ProfileVerified)
VALUES
    (3, 'Addiction Counseling', 'PhD in Psychology', 'CADC, LPC', 10, 4.8, 150, 1, 1),
    (4, 'Youth Substance Prevention', 'MSW', 'LCSW', 8, 4.7, 120, 1, 1),
    (9, 'Family Therapy', 'PhD in Family Therapy', 'LMFT', 12, 4.9, 200, 1, 1),
    (1, 'Cognitive Behavioral Therapy', 'PsyD', 'LPC, NCC', 15, 4.9, 250, 0, 1),
    (5, 'Group Therapy', 'MA in Counseling', 'LCPC', 7, 4.6, 100, 1, 1),
    (2, 'Substance Abuse Recovery', 'PhD in Clinical Psychology', 'CADC II', 11, 4.8, 180, 1, 1),
    (6, 'Adolescent Counseling', 'MS in Clinical Psychology', 'LPC', 6, 4.5, 90, 1, 1),
    (7, 'Crisis Intervention', 'MSW', 'LCSW, CCTP', 9, 4.7, 130, 1, 1),
    (8, 'Motivational Interviewing', 'MA in Counseling Psychology', 'LADC', 5, 4.4, 70, 1, 1),
    (10, 'Trauma-Informed Care', 'PhD in Clinical Psychology', 'LPC, CCTP', 14, 4.9, 220, 1, 1);
GO

-- Insert appointments
INSERT INTO AppointmentsNganVHH (AppointmentDateTime, Duration, Status, ConsultantID, MeetingLink, FeedbackRating, FeedbackComments, IsCancelled, CreatedDate)
VALUES
    ('2024-05-25 10:00:00', 60, 'Scheduled', 1, 'https://meet.system.com/appt1', NULL, NULL, 0, '2024-05-10'),
    ('2024-05-26 14:00:00', 45, 'Scheduled', 2, 'https://meet.system.com/appt2', NULL, NULL, 0, '2024-05-11'),
    ('2024-05-24 11:00:00', 60, 'Completed', 3, 'https://meet.system.com/appt3', 5, 'Excellent session!', 0, '2024-05-09'),
    ('2024-05-23 13:30:00', 45, 'Completed', 1, 'https://meet.system.com/appt4', 4, 'Very helpful advice', 0, '2024-05-08'),
    ('2024-05-27 09:00:00', 60, 'Scheduled', 4, 'https://meet.system.com/appt5', NULL, NULL, 0, '2024-05-12'),
    ('2024-05-22 15:00:00', 45, 'Cancelled', 5, 'https://meet.system.com/appt6', NULL, NULL, 1, '2024-05-07'),
    ('2024-05-28 10:30:00', 60, 'Scheduled', 2, 'https://meet.system.com/appt7', NULL, NULL, 0, '2024-05-13'),
    ('2024-05-21 11:00:00', 45, 'No-show', 3, 'https://meet.system.com/appt8', NULL, NULL, 0, '2024-05-06'),
    ('2024-05-29 14:00:00', 60, 'Scheduled', 1, 'https://meet.system.com/appt9', NULL, NULL, 0, '2024-05-14'),
    ('2024-05-30 16:00:00', 45, 'Scheduled', 4, 'https://meet.system.com/appt10', NULL, NULL, 0, '2024-05-15');

-- Insert community programs
INSERT INTO CommunityProgramsToanNS (ProgramName, StartDate, EndDate, Location, MaxParticipants, CurrentParticipants, Status, OrganizerId, IsActive)
VALUES
    ('Community Drug Awareness Workshop', '2024-06-15 09:00:00', '2024-06-15 16:00:00', 'Community Center', 50, 32, 'Active', 1, 1),
    ('Teen Peer Support Group', '2024-06-20 16:00:00', '2024-07-25 18:00:00', 'Youth Center', 20, 15, 'Active', 2, 1),
    ('Family Support Night', '2024-06-30 18:00:00', '2024-06-30 21:00:00', 'Public Library', 30, 25, 'Planning', 3, 1),
    ('School Prevention Program', '2024-07-05 08:00:00', '2024-07-09 15:00:00', 'Lincoln High School', 100, 80, 'Planning', 5, 1),
    ('Recovery Celebration Event', '2024-07-15 17:00:00', '2024-07-15 21:00:00', 'City Park', 200, 120, 'Planning', 1, 1),
    ('Parent Education Series', '2024-06-05 18:00:00', '2024-06-26 20:00:00', 'Community Center', 40, 38, 'Completed', 2, 1),
    ('Workplace Prevention Training', '2024-07-20 09:00:00', '2024-07-20 16:00:00', 'Business Center', 75, 45, 'Planning', 1, 1),
    ('Youth Summer Camp', '2024-08-01 08:00:00', '2024-08-05 17:00:00', 'Camp Willow', 80, 65, 'Planning', 4, 1),
    ('Community Health Fair', '2024-08-15 10:00:00', '2024-08-15 16:00:00', 'Downtown Plaza', 300, 150, 'Planning', 1, 1),
    ('Recovery Walk', '2024-09-01 09:00:00', '2024-09-01 12:00:00', 'Riverfront Park', 500, 200, 'Planning', 3, 1);
GO

-- Insert user courses
INSERT INTO UserCoursesTuyenTM (UserID, CourseID, EnrollmentDate, CompletionDate, Progress, Score, CertificateIssued, Rating, LastAccessDate)
VALUES
    (6, 1, '2024-04-01', '2024-04-15', 100, 92, 1, 5, '2024-04-15'),
    (7, 2, '2024-04-02', '2024-04-14', 100, 88, 1, 4, '2024-04-14'),
    (8, 3, '2024-04-03', NULL, 65, NULL, 0, NULL, '2024-05-10'),
    (6, 4, '2024-04-05', '2024-04-20', 100, 95, 1, 5, '2024-04-20'),
    (7, 5, '2024-04-10', '2024-04-25', 100, 85, 1, 4, '2024-04-25'),
    (8, 6, '2024-04-12', NULL, 45, NULL, 0, NULL, '2024-05-12'),
    (6, 7, '2024-04-15', '2024-05-01', 100, 90, 1, 5, '2024-05-01'),
    (7, 8, '2024-04-18', NULL, 80, NULL, 0, NULL, '2024-05-15'),
    (8, 9, '2024-04-20', NULL, 30, NULL, 0, NULL, '2024-05-18'),
    (10, 10, '2024-04-22', '2024-05-10', 100, 94, 1, 5, '2024-05-10');
GO

-- Insert user surveys
INSERT INTO UserSurveysNamND (UserID, SurveyID, SubmissionDate, RiskScore, Recommendations, CompletionTime, IsAnonymous, ResultSummary)
VALUES
    (6, 1, '2024-04-05', 25, 'Low risk. Continue with preventive education.', 15, 0, 'Low risk profile with good awareness.'),
    (7, 1, '2024-04-06', 45, 'Moderate risk. Consider counseling sessions.', 12, 0, 'Moderate risk with some concerning factors.'),
    (8, 2, '2024-04-08', 35, 'Low-moderate risk. Follow-up recommended.', 10, 1, 'Some risk factors present but generally low risk.'),
    (6, 3, '2024-04-10', 15, 'Very low risk. Maintain current approach.', 20, 0, 'Excellent recovery progress with minimal risk.'),
    (7, 4, '2024-04-15', NULL, 'Good knowledge base. Consider advanced course.', 18, 0, 'Strong understanding of prevention strategies.'),
    (8, 5, '2024-04-20', 65, 'Higher risk environment. Intervention needed.', 25, 1, 'Environmental factors present significant concerns.'),
    (NULL, 6, '2024-04-25', NULL, 'Community would benefit from more resources.', 30, 1, 'Identified gaps in community support systems.'),
    (6, 7, '2024-05-01', NULL, 'Highly satisfied with treatment approach.', 10, 0, 'Positive feedback on treatment effectiveness.'),
    (7, 8, '2024-05-05', 40, 'Moderate peer influence. Develop resistance skills.', 15, 0, 'Some vulnerability to negative peer influences.'),
    (NULL, 9, '2024-05-10', NULL, 'Policy awareness is good but implementation is lacking.', 20, 1, 'Identified opportunities for policy enhancement.');

GO

-- Insert user appointments
INSERT INTO UserAppointmentsNganVHH (UserID, AppointmentID, BookingDate, PrimaryReason, FollowUpRequired, PreAppointmentSurveyCompleted, PostAppointmentFeedbackProvided, AdditionalNotes, FollowUpNotes)
VALUES
    (6, 1, '2024-05-05', 'Initial consultation', 1, 1, 0, 'Client seeking information about prevention strategies', NULL),
    (7, 2, '2024-05-06', 'Support for family member', 0, 1, 0, 'Concerned about teenage son', NULL),
    (8, 3, '2024-05-01', 'Personal risk assessment', 1, 1, 1, 'History of substance use in family', 'Recommended family therapy sessions'),
    (6, 4, '2024-05-02', 'Follow-up session', 0, 1, 1, 'Progress check after initial consultation', 'Client showing good progress'),
    (7, 5, '2024-05-07', 'Parenting strategies', 1, 0, 0, 'Seeking advice on communication with teens', NULL),
    (8, 6, '2024-04-30', 'Crisis intervention', 0, 0, 0, 'Urgent meeting requested', 'Client cancelled - rescheduling needed'),
    (6, 7, '2024-05-08', 'Program information', 0, 1, 0, 'Interested in community programs', NULL),
    (7, 8, '2024-04-29', 'Support group information', 0, 1, 0, 'Looking for peer support options', 'Client did not show up'),
    (8, 9, '2024-05-09', 'Educational resources', 1, 1, 0, 'Seeking materials for school project', NULL),
    (6, 10, '2024-05-10', 'Recovery planning', 1, 1, 0, 'Creating long-term prevention plan', NULL);
GO

-- Insert program participants
INSERT INTO ProgramParticipantsToanNS (ProgramToanNSID, UserID, RegistrationDate, AttendanceStatus, FeedbackProvided, FeedbackRating, CertificateIssued, ParticipantRole, FeedbackComments)
VALUES
    (1, 6, '2024-05-01', 'Registered', 0, NULL, 0, 'Attendee', NULL),
    (1, 7, '2024-05-02', 'Registered', 0, NULL, 0, 'Attendee', NULL),
    (2, 8, '2024-05-05', 'Registered', 0, NULL, 0, 'Peer Support', NULL),
    (2, 6, '2024-05-06', 'Registered', 0, NULL, 0, 'Attendee', NULL),
    (3, 7, '2024-05-10', 'Registered', 0, NULL, 0, 'Family Member', NULL),
    (4, 8, '2024-05-12', 'Registered', 0, NULL, 0, 'Student', NULL),
    (5, 6, '2024-05-15', 'Registered', 0, NULL, 0, 'Volunteer', NULL),
    (6, 7, '2024-04-01', 'Attended', 1, 5, 1, 'Parent', 'Very informative series!'),
    (6, 8, '2024-04-02', 'Attended', 1, 4, 1, 'Guardian', 'Good information but could use more resources'),
    (6, 6, '2024-04-03', 'Absent', 0, NULL, 0, 'Parent', NULL);
GO

-- Insert survey questions
INSERT INTO SurveyQuestionsQuangTNV (SurveyID, QuestionText, QuestionType, Options, IsRequired, QuestionOrder, RiskWeight, HelpText, DependsOnQuestionID, DependsOnAnswer, ImageURL)
VALUES
    (1, 'How often do you consume alcoholic beverages?', 'SingleChoice', 'Never|Rarely|Sometimes|Often|Very Often', 1, 1, 2, 'Select the option that best describes your consumption', NULL, NULL, NULL),
    (1, 'Have you ever used prescription drugs without a prescription?', 'YesNo', 'Yes|No', 1, 2, 3, 'This includes medications not prescribed to you', NULL, NULL, NULL),
    (2, 'How comfortable are you discussing substance abuse with adults?', 'Likert', '1|2|3|4|5', 1, 1, 1, '1 = Very Uncomfortable, 5 = Very Comfortable', NULL, NULL, NULL),
    (2, 'What substances do you think are most commonly misused by your peers?', 'MultipleChoice', 'Alcohol|Marijuana|Prescription drugs|Tobacco|Other', 1, 2, 1, 'Select all that apply', NULL, NULL, NULL),
    (3, 'On a scale of 1-5, how effective was your treatment program?', 'Likert', '1|2|3|4|5', 1, 1, 0, '1 = Not effective, 5 = Very effective', NULL, NULL, NULL),
    (3, 'What challenges have you faced in maintaining sobriety?', 'OpenEnded', NULL, 1, 2, 0, 'Please describe any difficulties you have experienced', NULL, NULL, NULL),
    (4, 'What warning signs would you look for in adolescents?', 'MultipleChoice', 'Sudden behavioral changes|Declining grades|New friend groups|Secretive behavior|Physical symptoms|Loss of interest in activities', 1, 1, 0, 'Select all that apply', NULL, NULL, NULL),
    (5, 'Does your school have clear policies regarding substance abuse?', 'YesNo', 'Yes|No', 1, 1, 2, 'Consider formal written policies', NULL, NULL, NULL),
    (5, 'Rate the effectiveness of current prevention programs at your school', 'Likert', '1|2|3|4|5', 1, 2, 2, '1 = Not effective, 5 = Very effective', NULL, NULL, NULL),
    (6, 'What resources would most benefit your community?', 'MultipleChoice', 'Support groups|Education programs|Treatment facilities|Youth activities|Family services|Crisis intervention', 1, 1, 0, 'Select all that apply', NULL, NULL, NULL);

GO

-- Insert consultant schedules
INSERT INTO ConsultantScheduleTrongLH (ConsultantID, DayOfWeek, StartTime, EndTime, IsAvailable, RecurringPattern, EffectiveFrom, EffectiveTo, MaxAppointmentsPerSlot, BufferMinutesBetweenMeetings, Notes)
VALUES
    (1, 1, '09:00:00', '17:00:00', 1, 'Weekly', '2024-05-01', '2024-07-31', 6, 15, 'Monday full day availability'),
    (1, 3, '09:00:00', '17:00:00', 1, 'Weekly', '2024-05-01', '2024-07-31', 6, 15, 'Wednesday full day availability'),
    (2, 2, '10:00:00', '18:00:00', 1, 'Weekly', '2024-05-01', '2024-07-31', 5, 15, 'Tuesday full day availability'),
    (2, 4, '10:00:00', '18:00:00', 1, 'Weekly', '2024-05-01', '2024-07-31', 5, 15, 'Thursday full day availability'),
    (3, 1, '12:00:00', '20:00:00', 1, 'Weekly', '2024-05-01', '2024-07-31', 6, 15, 'Monday afternoon/evening availability'),
    (3, 5, '09:00:00', '17:00:00', 1, 'Weekly', '2024-05-01', '2024-07-31', 6, 15, 'Friday full day availability'),
    (4, 2, '08:00:00', '16:00:00', 1, 'Weekly', '2024-05-01', '2024-07-31', 5, 15, 'Tuesday full day availability'),
    (5, 3, '13:00:00', '21:00:00', 1, 'Weekly', '2024-05-01', '2024-07-31', 6, 15, 'Wednesday afternoon/evening availability'),
    (6, 4, '09:00:00', '17:00:00', 1, 'Weekly', '2024-05-01', '2024-07-31', 6, 15, 'Thursday full day availability'),
    (7, 5, '10:00:00', '18:00:00', 1, 'Weekly', '2024-05-01', '2024-07-31', 5, 15, 'Friday full day availability');
GO
