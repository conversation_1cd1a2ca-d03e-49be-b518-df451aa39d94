{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "DrugPrevention.BlazorApp.TuyenTM.9esl1qwdnb.styles.css", "AssetFile": "DrugPrevention.BlazorApp.TuyenTM.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "5947"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AQyMIlxFC+haJdDxK8wLnRHr5Ko/XXzWIp6m+/24hLI=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:53:15 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9esl1qwdnb"}, {"Name": "integrity", "Value": "sha256-AQyMIlxFC+haJdDxK8wLnRHr5Ko/XXzWIp6m+/24hLI="}, {"Name": "label", "Value": "DrugPrevention.BlazorApp.TuyenTM.styles.css"}]}, {"Route": "DrugPrevention.BlazorApp.TuyenTM.styles.css", "AssetFile": "DrugPrevention.BlazorApp.TuyenTM.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "5947"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"AQyMIlxFC+haJdDxK8wLnRHr5Ko/XXzWIp6m+/24hLI=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:53:15 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AQyMIlxFC+haJdDxK8wLnRHr5Ko/XXzWIp6m+/24hLI="}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:31 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "app.da95v2qkru.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:31 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "da95v2qkru"}, {"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:31 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:31 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:31 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:31 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/style.css", "AssetFile": "css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6170"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Mf2dBpwYG7NFLDLoMz47duMrtr4ocT9054HTh/pbHMY=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 07:45:51 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Mf2dBpwYG7NFLDLoMz47duMrtr4ocT9054HTh/pbHMY="}]}, {"Route": "css/style.hkxpjpllzs.css", "AssetFile": "css/style.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6170"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Mf2dBpwYG7NFLDLoMz47duMrtr4ocT9054HTh/pbHMY=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 07:45:51 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hkxpjpllzs"}, {"Name": "integrity", "Value": "sha256-Mf2dBpwYG7NFLDLoMz47duMrtr4ocT9054HTh/pbHMY="}, {"Name": "label", "Value": "css/style.css"}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:31 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}, {"Name": "label", "Value": "favicon.png"}]}, {"Route": "favicon.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=3600, must-revalidate"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:31 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}]}