{"format": 1, "restore": {"D:\\FPTU\\S7\\PRN222\\SU25_PRN222_SE1709_ASM4_SE183888_TuyenTM\\DrugPrevention.Services.TuyenTM\\DrugPrevention.Services.TuyenTM.csproj": {}}, "projects": {"D:\\FPTU\\S7\\PRN222\\SU25_PRN222_SE1709_ASM4_SE183888_TuyenTM\\DrugPrevention.Repositories.TuyenTM\\DrugPrevention.Repositories.TuyenTM.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\S7\\PRN222\\SU25_PRN222_SE1709_ASM4_SE183888_TuyenTM\\DrugPrevention.Repositories.TuyenTM\\DrugPrevention.Repositories.TuyenTM.csproj", "projectName": "DrugPrevention.Repositories.TuyenTM", "projectPath": "D:\\FPTU\\S7\\PRN222\\SU25_PRN222_SE1709_ASM4_SE183888_TuyenTM\\DrugPrevention.Repositories.TuyenTM\\DrugPrevention.Repositories.TuyenTM.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\S7\\PRN222\\SU25_PRN222_SE1709_ASM4_SE183888_TuyenTM\\DrugPrevention.Repositories.TuyenTM\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.5, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.5, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}, "D:\\FPTU\\S7\\PRN222\\SU25_PRN222_SE1709_ASM4_SE183888_TuyenTM\\DrugPrevention.Services.TuyenTM\\DrugPrevention.Services.TuyenTM.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\FPTU\\S7\\PRN222\\SU25_PRN222_SE1709_ASM4_SE183888_TuyenTM\\DrugPrevention.Services.TuyenTM\\DrugPrevention.Services.TuyenTM.csproj", "projectName": "DrugPrevention.Services.TuyenTM", "projectPath": "D:\\FPTU\\S7\\PRN222\\SU25_PRN222_SE1709_ASM4_SE183888_TuyenTM\\DrugPrevention.Services.TuyenTM\\DrugPrevention.Services.TuyenTM.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\FPTU\\S7\\PRN222\\SU25_PRN222_SE1709_ASM4_SE183888_TuyenTM\\DrugPrevention.Services.TuyenTM\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\FPTU\\S7\\PRN222\\SU25_PRN222_SE1709_ASM4_SE183888_TuyenTM\\DrugPrevention.Repositories.TuyenTM\\DrugPrevention.Repositories.TuyenTM.csproj": {"projectPath": "D:\\FPTU\\S7\\PRN222\\SU25_PRN222_SE1709_ASM4_SE183888_TuyenTM\\DrugPrevention.Repositories.TuyenTM\\DrugPrevention.Repositories.TuyenTM.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}