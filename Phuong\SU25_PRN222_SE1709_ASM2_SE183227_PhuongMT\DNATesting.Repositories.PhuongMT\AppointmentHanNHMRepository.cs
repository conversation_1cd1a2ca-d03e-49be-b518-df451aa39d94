﻿using DNATesting.Repositories.PhuongMT.Basic;
using DNATesting.Repositories.PhuongMT.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Repositories.PhuongMT
{
    public class AppointmentHanNHMRepository : GenericRepository<AppointmentHanNhm>
    {
        public AppointmentHanNHMRepository() { }
        public AppointmentHanNHMRepository(SE18_PRN222_SE1709_G3_DNA_TestingContext context) => _context = context;
    }
}
