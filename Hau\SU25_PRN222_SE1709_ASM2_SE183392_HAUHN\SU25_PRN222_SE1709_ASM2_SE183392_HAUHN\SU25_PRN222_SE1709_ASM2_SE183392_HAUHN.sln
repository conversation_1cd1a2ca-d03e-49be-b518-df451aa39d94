﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36121.58
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DNATesting.Repositories.HauHN", "DNATesting.Repositories.HauHN\DNATesting.Repositories.HauHN.csproj", "{437EDC6D-AF09-9802-6F84-CFAD35FD4199}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DNATesting.Services.HauHN", "DNATesting.Services.HauHN\DNATesting.Services.HauHN.csproj", "{0D9D7F64-9698-C842-68F0-D4F9EC407E85}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DNATesting.BlazorWebApp.HauHN", "DNATesting.BlazorWebApp.HauHN\DNATesting.BlazorWebApp.HauHN.csproj", "{95091A98-6C4B-4D82-AD86-DD335D152BB5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{437EDC6D-AF09-9802-6F84-CFAD35FD4199}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{437EDC6D-AF09-9802-6F84-CFAD35FD4199}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{437EDC6D-AF09-9802-6F84-CFAD35FD4199}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{437EDC6D-AF09-9802-6F84-CFAD35FD4199}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D9D7F64-9698-C842-68F0-D4F9EC407E85}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D9D7F64-9698-C842-68F0-D4F9EC407E85}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D9D7F64-9698-C842-68F0-D4F9EC407E85}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D9D7F64-9698-C842-68F0-D4F9EC407E85}.Release|Any CPU.Build.0 = Release|Any CPU
		{95091A98-6C4B-4D82-AD86-DD335D152BB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{95091A98-6C4B-4D82-AD86-DD335D152BB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{95091A98-6C4B-4D82-AD86-DD335D152BB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{95091A98-6C4B-4D82-AD86-DD335D152BB5}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {88F9710D-2E1B-4FA7-B5E9-A47280473EBA}
	EndGlobalSection
EndGlobal
