﻿@model DrugPrevention.Repositories.TuyenTM.Models.OrganizationProgramsTuyenTM

@{
    ViewData["Title"] = "Delete Program";
}

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>Delete Organization Program
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-warning me-2"></i>
                        <strong>Warning!</strong> Are you sure you want to delete this organization program? This action cannot be undone.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-building me-1"></i>Organization
                                </label>
                                <div class="p-2 bg-light rounded">
                                    <span class="badge bg-warning text-dark">@Html.DisplayFor(model => model.Organization.OrganizationName)</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-project-diagram me-1"></i>Program
                                </label>
                                <div class="p-2 bg-light rounded">
                                    <span class="badge bg-info">@Html.DisplayFor(model => model.ProgramToanNS.ProgramName)</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-calendar me-1"></i>Joined Date
                                </label>
                                <div class="p-2 bg-light rounded">
                                    <span class="badge bg-info">@Model.JoinedDate.ToString("MMMM dd, yyyy")</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-user-tag me-1"></i>Program Role
                                </label>
                                <div class="p-2 bg-light rounded">
                                    <span class="badge bg-primary">@Html.DisplayFor(model => model.ProgramRole)</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">
                            <i class="fas fa-comment me-1"></i>Contribution Description
                        </label>
                        <div class="p-3 bg-light rounded">
                            @Html.DisplayFor(model => model.ContributionDescription)
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-dollar-sign me-1"></i>Is Sponsor
                                </label>
                                <div class="p-2 bg-light rounded text-center">
                                    @if(Model.IsSponsor == true) {
                                        <span class="badge bg-success"><i class="fas fa-check"></i> Yes</span>
                                    } else if(Model.IsSponsor == false) {
                                        <span class="badge bg-secondary"><i class="fas fa-times"></i> No</span>
                                    } else {
                                        <span class="badge bg-light text-dark">N/A</span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-users me-1"></i>Is Organizer
                                </label>
                                <div class="p-2 bg-light rounded text-center">
                                    @if(Model.IsOrganizer == true) {
                                        <span class="badge bg-success"><i class="fas fa-check"></i> Yes</span>
                                    } else if(Model.IsOrganizer == false) {
                                        <span class="badge bg-secondary"><i class="fas fa-times"></i> No</span>
                                    } else {
                                        <span class="badge bg-light text-dark">N/A</span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-money-bill me-1"></i>Funding Amount
                                </label>
                                <div class="p-2 bg-light rounded text-center">
                                    @if(Model.FundingAmount.HasValue) {
                                        <span class="text-success fw-bold">$@Model.FundingAmount.Value.ToString("N2")</span>
                                    } else {
                                        <span class="text-muted">No funding</span>
                                    }
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label text-muted">
                                    <i class="fas fa-clock me-1"></i>Last Updated
                                </label>
                                <div class="p-2 bg-light rounded text-center">
                                    @if(Model.LastUpdated.HasValue) {
                                        <small class="text-muted">@Model.LastUpdated.Value.ToString("MMM dd, yyyy")</small>
                                    } else {
                                        <small class="text-muted">-</small>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label text-muted">
                            <i class="fas fa-star me-1"></i>Feedback
                        </label>
                        <div class="p-3 bg-light rounded">
                            @if(!string.IsNullOrEmpty(Model.Feedback)) {
                                @Html.DisplayFor(model => model.Feedback)
                            } else {
                                <span class="text-muted fst-italic">No feedback provided</span>
                            }
                        </div>
                    </div>

                    <form asp-action="Delete">
                        <input type="hidden" asp-for="OrganizationProgramTuyenTMID" />
                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i>Back to List
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>Yes, Delete This Program
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
