﻿@page
@model DrugPrevention.RazorWebApp.TuyenTM.Pages.OrganizationProgramTuyenTM.DeleteModel

<h3 class="mb-4">Are you sure you want to delete this?</h3>

<div>
    <h4>Organization Program Details</h4>
    <hr />
    <dl class="row">
        <dt class="col-sm-4"> @Html.DisplayNameFor(model => model.OrganizationProgramsTuyenTM.JoinedDate) </dt>
        <dd class="col-sm-8"> @Html.DisplayFor(model => model.OrganizationProgramsTuyenTM.JoinedDate) </dd>

        <dt class="col-sm-4"> @Html.DisplayNameFor(model => model.OrganizationProgramsTuyenTM.ContributionDescription) </dt>
        <dd class="col-sm-8"> @Html.DisplayFor(model => model.OrganizationProgramsTuyenTM.ContributionDescription) </dd>

        <dt class="col-sm-4"> @Html.DisplayNameFor(model => model.OrganizationProgramsTuyenTM.IsSponsor) </dt>
        <dd class="col-sm-8"> @Html.DisplayFor(model => model.OrganizationProgramsTuyenTM.IsSponsor) </dd>

        <dt class="col-sm-4"> @Html.DisplayNameFor(model => model.OrganizationProgramsTuyenTM.IsOrganizer) </dt>
        <dd class="col-sm-8"> @Html.DisplayFor(model => model.OrganizationProgramsTuyenTM.IsOrganizer) </dd>

        <dt class="col-sm-4"> @Html.DisplayNameFor(model => model.OrganizationProgramsTuyenTM.ProgramRole) </dt>
        <dd class="col-sm-8"> @Html.DisplayFor(model => model.OrganizationProgramsTuyenTM.ProgramRole) </dd>

        <dt class="col-sm-4"> @Html.DisplayNameFor(model => model.OrganizationProgramsTuyenTM.FundingAmount) </dt>
        <dd class="col-sm-8"> @Html.DisplayFor(model => model.OrganizationProgramsTuyenTM.FundingAmount) </dd>

        <dt class="col-sm-4"> @Html.DisplayNameFor(model => model.OrganizationProgramsTuyenTM.Feedback) </dt>
        <dd class="col-sm-8"> @Html.DisplayFor(model => model.OrganizationProgramsTuyenTM.Feedback) </dd>

        <dt class="col-sm-4"> @Html.DisplayNameFor(model => model.OrganizationProgramsTuyenTM.LastUpdated) </dt>
        <dd class="col-sm-8"> @Html.DisplayFor(model => model.OrganizationProgramsTuyenTM.LastUpdated) </dd>

        <dt class="col-sm-4"> @Html.DisplayNameFor(model => model.OrganizationProgramsTuyenTM.Organization) </dt>
        <dd class="col-sm-8"> @Html.DisplayFor(model => model.OrganizationProgramsTuyenTM.Organization.OrganizationName) </dd>

        <dt class="col-sm-4"> @Html.DisplayNameFor(model => model.OrganizationProgramsTuyenTM.ProgramToanNS) </dt>
        <dd class="col-sm-8"> @Html.DisplayFor(model => model.OrganizationProgramsTuyenTM.ProgramToanNS.ProgramName) </dd>
    </dl>

    <div class="row">
        <div class="col-sm-4"></div>
        <div class="col-sm-8">
            <form method="post" class="d-inline">
                <input type="hidden" asp-for="OrganizationProgramsTuyenTM.OrganizationProgramTuyenTMID" />
                <input type="submit" value="Delete" class="btn btn-danger me-2" />
                <input type="button" id="btnHubDelete" value="Delete by SignalRHub" class="btn btn-primary" />
            </form>
            <a asp-page="./Index" class="btn btn-secondary">Back to List</a>
        </div>
    </div>
</div>
<script src="~/js/signalr/dist/browser/signalr.js"></script>
<script>
    "use strict";

    var connection = new signalR.HubConnectionBuilder().withUrl("/DrugPreventionHub").build();

    connection.start().then(function () {
        console.log("Connected to the SignalR Hub");
    }).catch(function (err) {
        return console.error(err.toString());
    });

    document.getElementById("btnHubDelete").addEventListener("click", function (event) {

        event.preventDefault();

        var dataObj = document.getElementById("OrganizationProgramsTuyenTM_OrganizationProgramTuyenTMID").value;
        console.log(dataObj);

        connection.invoke("HubDelete_OrganizationProgramTuyenTM", dataObj).catch(function (err) {
            return console.error(err.toString());
        });
    });

     connection.on("OrganizationProgramDeletedRedirect", function () {
        window.location.href = "/OrganizationProgramTuyenTM/Index";
    });

</script>