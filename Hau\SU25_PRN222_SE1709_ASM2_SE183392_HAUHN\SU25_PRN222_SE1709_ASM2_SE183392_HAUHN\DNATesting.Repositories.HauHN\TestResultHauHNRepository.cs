﻿using DNATesting.Repositories.HauHN.Basic;
using DNATesting.Repositories.HauHN.DBContext;
using DNATesting.Repositories.HauHN.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Repositories.HauHN
{
    public class TestResultHauHNRepository : GenericRepository<TestResultHauHn>
    {
        public TestResultHauHNRepository() { }
        public TestResultHauHNRepository(SE18_PRN222_SE1709_G3_DNA_TestingContext context) => _context = context;
        public async Task<List<TestResultHauHn>> GetAllAsync()
        {
            var resultList = await _context.TestResultHauHns.Include(t => t.TestResultDetailsHauHns)
                .Include(t => t.AppointmentHanNhm)
                .Include(t => t.VerifiedByNavigation)
                .ToListAsync();
            return resultList ?? new List<TestResultHauHn>();
        }

        public async Task<TestResultHauHn> GetByIdAsync(String id)
        {
            var item = await _context.TestResultHauHns.Include(t => t.TestResultDetailsHauHns)
                .Include(t => t.AppointmentHanNhm)
                .Include(t=> t.Technician)
                .Include(t => t.VerifiedByNavigation).FirstOrDefaultAsync(t => t.ResultHauHnid == id);
            return item ?? new TestResultHauHn();
        }
        public async Task<bool> TestResultHauHnExistsAsync(String id)
        {
            return await _context.TestResultHauHns.AnyAsync(e => e.ResultHauHnid == id);
        }
        public async Task<List<TestResultHauHn>> SearchAsync(string code, bool? isVerified, string ApId)
        {
            var item = await _context.TestResultHauHns.Include(t => t.AppointmentHanNhm).Where(t =>
            (string.IsNullOrEmpty(code) || t.ResultHauHnid.Contains(code))
            && (isVerified == null || t.IsVerified == isVerified)
            && (string.IsNullOrEmpty(ApId) || (t.AppointmentHanNhm.AppointmentHanNhmid != null && t.AppointmentHanNhm.AppointmentHanNhmid.Contains(ApId)))).ToListAsync();
            return item ?? new List<TestResultHauHn>();
        }
        public async Task<int> UpdateAsync(TestResultHauHn testResult)
        {
            var entityInDb = await _context.TestResultHauHns.FindAsync(testResult.ResultHauHnid);

            if (entityInDb == null)
                return 0;

            entityInDb.AppointmentHanNhmid = testResult.AppointmentHanNhmid;
            entityInDb.TestType = testResult.TestType;
            entityInDb.ResultValue = testResult.ResultValue;
            entityInDb.ResultDate = testResult.ResultDate;
            entityInDb.TestDate = testResult.TestDate;
            entityInDb.TechnicianId = testResult.TechnicianId;
            entityInDb.IsVerified = testResult.IsVerified;
            entityInDb.VerifiedBy = testResult.VerifiedBy;
            entityInDb.GeneticMutation = testResult.GeneticMutation;
            entityInDb.DiseaseRisk = testResult.DiseaseRisk;
            entityInDb.RelationshipProbability = testResult.RelationshipProbability;

            // Now when you save, the new IDs will be persisted to the database.
            var tracker = _context.Attach(entityInDb);
            tracker.State = EntityState.Modified;
            return await _context.SaveChangesAsync();
        }

    }
}
