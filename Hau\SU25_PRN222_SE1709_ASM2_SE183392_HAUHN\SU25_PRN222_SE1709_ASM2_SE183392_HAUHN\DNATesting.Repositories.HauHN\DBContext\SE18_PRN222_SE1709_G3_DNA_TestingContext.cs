﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using DNATesting.Repositories.HauHN.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;

namespace DNATesting.Repositories.HauHN.DBContext;

public partial class SE18_PRN222_SE1709_G3_DNA_TestingContext : DbContext
{
    public SE18_PRN222_SE1709_G3_DNA_TestingContext()
    {
    }

    public SE18_PRN222_SE1709_G3_DNA_TestingContext(DbContextOptions<SE18_PRN222_SE1709_G3_DNA_TestingContext> options)
        : base(options)
    {
    }

    public virtual DbSet<AppointmentDetailHanNhm> AppointmentDetailHanNhms { get; set; }

    public virtual DbSet<AppointmentHanNhm> AppointmentHanNhms { get; set; }

    public virtual DbSet<DnatestOptionDetailKhanhHn> DnatestOptionDetailKhanhHns { get; set; }

    public virtual DbSet<DnatestOptionKhanhHn> DnatestOptionKhanhHns { get; set; }

    public virtual DbSet<FeedbackHoangDn> FeedbackHoangDns { get; set; }

    public virtual DbSet<ProcessLogPhuongMt> ProcessLogPhuongMts { get; set; }

    public virtual DbSet<ReportDetailTinPt> ReportDetailTinPts { get; set; }

    public virtual DbSet<ReportTinPt> ReportTinPts { get; set; }

    public virtual DbSet<SamplePhuongMt> SamplePhuongMts { get; set; }

    public virtual DbSet<SystemUserAccount> SystemUserAccounts { get; set; }

    public virtual DbSet<TestParticipantsHauHn> TestParticipantsHauHns { get; set; }

    public virtual DbSet<TestResultDetailsHauHn> TestResultDetailsHauHns { get; set; }

    public virtual DbSet<TestResultHauHn> TestResultHauHns { get; set; }

    public virtual DbSet<UserRole> UserRoles { get; set; }
    public static string GetConnectionString(string connectionStringName)
    {
        var config = new ConfigurationBuilder()
            .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
            .AddJsonFile("appsettings.json")
            .Build();

        string connectionString = config.GetConnectionString(connectionStringName);
        return connectionString;
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        => optionsBuilder.UseSqlServer(GetConnectionString("DefaultConnection")).UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
    //    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    //#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
    //        => optionsBuilder.UseSqlServer("Data Source=HAUHANDSOME\\HAUHUYNH;Initial Catalog=SE18_PRN222_SE1709_G3_DNA_Testing;Persist Security Info=True;User ID=sa;Password=*****;Encrypt=False");

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<AppointmentDetailHanNhm>(entity =>
        {
            entity.HasKey(e => e.AppointmentDetailHanNhmid).HasName("PK__Appointm__21B13FB1863BECC8");

            entity.ToTable("AppointmentDetailHanNHM");

            entity.Property(e => e.AppointmentDetailHanNhmid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("AppointmentDetailHanNHMID");
            entity.Property(e => e.AppointmentDate).HasColumnType("datetime");
            entity.Property(e => e.AppointmentHanNhmid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("AppointmentHanNHMID");
            entity.Property(e => e.CanceledAt).HasColumnType("datetime");
            entity.Property(e => e.CollectedBy).HasMaxLength(100);
            entity.Property(e => e.SampleType).HasMaxLength(50);
            entity.Property(e => e.Status).HasMaxLength(50);
            entity.Property(e => e.TechnicianId).HasColumnName("technicianId");
            entity.Property(e => e.TotalPrice).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.AppointmentHanNhm).WithMany(p => p.AppointmentDetailHanNhms)
                .HasForeignKey(d => d.AppointmentHanNhmid)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK__Appointme__Appoi__30F848ED");

            entity.HasOne(d => d.Technician).WithMany(p => p.AppointmentDetailHanNhmTechnicians)
                .HasForeignKey(d => d.TechnicianId)
                .HasConstraintName("FK__Appointme__techn__32E0915F");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.AppointmentDetailHanNhmUserAccounts)
                .HasForeignKey(d => d.UserAccountId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__Appointme__UserA__31EC6D26");
        });

        modelBuilder.Entity<AppointmentHanNhm>(entity =>
        {
            entity.HasKey(e => e.AppointmentHanNhmid).HasName("PK__Appointm__46F600EBE55C920C");

            entity.ToTable("AppointmentHanNHM");

            entity.Property(e => e.AppointmentHanNhmid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("AppointmentHanNHMID");
            entity.Property(e => e.ApplicationSource).HasMaxLength(50);
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.AppointmentHanNhms)
                .HasForeignKey(d => d.UserAccountId)
                .HasConstraintName("FK__Appointme__UserA__2E1BDC42");

            entity.HasMany(d => d.DnatestOptionKhanhHns).WithMany(p => p.AppointmentHanNhms)
                .UsingEntity<Dictionary<string, object>>(
                    "AppointmentOptionMapping",
                    r => r.HasOne<DnatestOptionKhanhHn>().WithMany()
                        .HasForeignKey("DnatestOptionKhanhHnid")
                        .HasConstraintName("FK__Appointme__DNATe__36B12243"),
                    l => l.HasOne<AppointmentHanNhm>().WithMany()
                        .HasForeignKey("AppointmentHanNhmid")
                        .HasConstraintName("FK__Appointme__Appoi__35BCFE0A"),
                    j =>
                    {
                        j.HasKey("AppointmentHanNhmid", "DnatestOptionKhanhHnid").HasName("PK__Appointm__82CFD797B7BF46E3");
                        j.ToTable("AppointmentOptionMapping");
                        j.IndexerProperty<string>("AppointmentHanNhmid")
                            .HasMaxLength(10)
                            .IsUnicode(false)
                            .HasColumnName("AppointmentHanNHMID");
                        j.IndexerProperty<string>("DnatestOptionKhanhHnid")
                            .HasMaxLength(10)
                            .IsUnicode(false)
                            .HasColumnName("DNATestOptionKhanhHNID");
                    });
        });

        modelBuilder.Entity<DnatestOptionDetailKhanhHn>(entity =>
        {
            entity.HasKey(e => e.DnatestOptionDetailKhanhHnid).HasName("PK__DNATestO__FB423B84C772A9CD");

            entity.ToTable("DNATestOptionDetailKhanhHN");

            entity.Property(e => e.DnatestOptionDetailKhanhHnid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("DNATestOptionDetailKhanhHNID");
            entity.Property(e => e.ComponentPrice).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.CreatBy).HasMaxLength(100);
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.DnatestOptionKhanhHnid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("DNATestOptionKhanhHNID");
            entity.Property(e => e.LastUpdated).HasColumnType("datetime");
            entity.Property(e => e.ResultFormat).HasMaxLength(100);
            entity.Property(e => e.SampleMethod).HasMaxLength(50);
            entity.Property(e => e.SampleRole).HasMaxLength(50);
            entity.Property(e => e.SubOptionName).HasMaxLength(100);
            entity.Property(e => e.UpdateBy).HasMaxLength(100);

            entity.HasOne(d => d.DnatestOptionKhanhHn).WithMany(p => p.DnatestOptionDetailKhanhHns)
                .HasForeignKey(d => d.DnatestOptionKhanhHnid)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK__DNATestOp__DNATe__2B3F6F97");
        });

        modelBuilder.Entity<DnatestOptionKhanhHn>(entity =>
        {
            entity.HasKey(e => e.DnatestOptionKhanhHnid).HasName("PK__DNATestO__439D77CD531E62D5");

            entity.ToTable("DNATestOptionKhanhHN");

            entity.Property(e => e.DnatestOptionKhanhHnid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("DNATestOptionKhanhHNID");
            entity.Property(e => e.CreatBy).HasMaxLength(100);
            entity.Property(e => e.CreatedAt).HasColumnType("datetime");
            entity.Property(e => e.LastUpdated).HasColumnType("datetime");
            entity.Property(e => e.OptionName).HasMaxLength(100);
            entity.Property(e => e.OptionType).HasMaxLength(100);
            entity.Property(e => e.Price).HasColumnType("decimal(10, 2)");
            entity.Property(e => e.RecommendedUse).HasMaxLength(200);
            entity.Property(e => e.UpdateBy).HasMaxLength(100);
        });

        modelBuilder.Entity<FeedbackHoangDn>(entity =>
        {
            entity.HasKey(e => e.FeedbackHoangDnid).HasName("PK__Feedback__22E8F88955FCB350");

            entity.ToTable("FeedbackHoangDN");

            entity.Property(e => e.FeedbackHoangDnid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("FeedbackHoangDNID");
            entity.Property(e => e.FeedbackAt).HasColumnType("datetime");
            entity.Property(e => e.OptionIdkhanhHn)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("OptionIDKhanhHN");
            entity.Property(e => e.ResponseAt).HasColumnType("datetime");
            entity.Property(e => e.StaffId).HasColumnName("StaffID");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.OptionIdkhanhHnNavigation).WithMany(p => p.FeedbackHoangDns)
                .HasForeignKey(d => d.OptionIdkhanhHn)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK__FeedbackH__Optio__5535A963");

            entity.HasOne(d => d.Staff).WithMany(p => p.FeedbackHoangDnStaffs)
                .HasForeignKey(d => d.StaffId)
                .HasConstraintName("FK__FeedbackH__Staff__5441852A");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.FeedbackHoangDnUserAccounts)
                .HasForeignKey(d => d.UserAccountId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__FeedbackH__UserA__534D60F1");
        });

        modelBuilder.Entity<ProcessLogPhuongMt>(entity =>
        {
            entity.HasKey(e => e.LogPhuongMtid).HasName("PK__ProcessL__9E5B3DB589D601B3");

            entity.ToTable("ProcessLogPhuongMT");

            entity.Property(e => e.LogPhuongMtid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("LogPhuongMTID");
            entity.Property(e => e.ActionType).HasMaxLength(100);
            entity.Property(e => e.SamplePhuongMtid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("SamplePhuongMTID");
            entity.Property(e => e.Time).HasColumnType("datetime");
            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");

            entity.HasOne(d => d.SamplePhuongMt).WithMany(p => p.ProcessLogPhuongMts)
                .HasForeignKey(d => d.SamplePhuongMtid)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK__ProcessLo__Sampl__4F7CD00D");

            entity.HasOne(d => d.UserAccount).WithMany(p => p.ProcessLogPhuongMts)
                .HasForeignKey(d => d.UserAccountId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__ProcessLo__UserA__5070F446");
        });

        modelBuilder.Entity<ReportDetailTinPt>(entity =>
        {
            entity.HasKey(e => e.ReportDetailTinPtid).HasName("PK__ReportDe__49C91A8546FFC9A4");

            entity.ToTable("ReportDetailTinPT");

            entity.Property(e => e.ReportDetailTinPtid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("ReportDetailTinPTID");
            entity.Property(e => e.DateEnd).HasColumnType("datetime");
            entity.Property(e => e.DateStart).HasColumnType("datetime");
            entity.Property(e => e.GeneratedAt).HasColumnType("datetime");
            entity.Property(e => e.OptionIdkhanhHn)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("OptionIDKhanhHN");
            entity.Property(e => e.ReasonForReport).HasMaxLength(255);
            entity.Property(e => e.ReportTinPtid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("ReportTinPTID");
            entity.Property(e => e.RevenueNote).HasMaxLength(255);
            entity.Property(e => e.TotalRevenue).HasColumnType("decimal(10, 2)");

            entity.HasOne(d => d.OptionIdkhanhHnNavigation).WithMany(p => p.ReportDetailTinPts)
                .HasForeignKey(d => d.OptionIdkhanhHn)
                .HasConstraintName("FK__ReportDet__Optio__5AEE82B9");

            entity.HasOne(d => d.ReportTinPt).WithMany(p => p.ReportDetailTinPts)
                .HasForeignKey(d => d.ReportTinPtid)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK__ReportDet__Repor__5BE2A6F2");
        });

        modelBuilder.Entity<ReportTinPt>(entity =>
        {
            entity.HasKey(e => e.ReportTinPtid).HasName("PK__ReportTi__3797C6BB8934BF46");

            entity.ToTable("ReportTinPT");

            entity.Property(e => e.ReportTinPtid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("ReportTinPTID");
            entity.Property(e => e.DateEnd).HasColumnType("datetime");
            entity.Property(e => e.DateStart).HasColumnType("datetime");
            entity.Property(e => e.GeneratedAt).HasColumnType("datetime");
            entity.Property(e => e.Notes).HasMaxLength(255);

            entity.HasOne(d => d.GeneratedByNavigation).WithMany(p => p.ReportTinPts)
                .HasForeignKey(d => d.GeneratedBy)
                .HasConstraintName("FK__ReportTin__Gener__5812160E");

            entity.HasMany(d => d.AppointmentHanNhms).WithMany(p => p.ReportTinPts)
                .UsingEntity<Dictionary<string, object>>(
                    "ReportAppointmentMapping",
                    r => r.HasOne<AppointmentHanNhm>().WithMany()
                        .HasForeignKey("AppointmentHanNhmid")
                        .HasConstraintName("FK__ReportApp__Appoi__5FB337D6"),
                    l => l.HasOne<ReportTinPt>().WithMany()
                        .HasForeignKey("ReportTinPtid")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("FK__ReportApp__Repor__5EBF139D"),
                    j =>
                    {
                        j.HasKey("ReportTinPtid", "AppointmentHanNhmid").HasName("PK__ReportAp__93F8A6B527BED4D4");
                        j.ToTable("ReportAppointmentMapping");
                        j.IndexerProperty<string>("ReportTinPtid")
                            .HasMaxLength(10)
                            .IsUnicode(false)
                            .HasColumnName("ReportTinPTID");
                        j.IndexerProperty<string>("AppointmentHanNhmid")
                            .HasMaxLength(10)
                            .IsUnicode(false)
                            .HasColumnName("AppointmentHanNHMID");
                    });
        });

        modelBuilder.Entity<SamplePhuongMt>(entity =>
        {
            entity.HasKey(e => e.SamplePhuongMtid).HasName("PK__SamplePh__77F651EFA063F08D");

            entity.ToTable("SamplePhuongMT");

            entity.Property(e => e.SamplePhuongMtid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("SamplePhuongMTID");
            entity.Property(e => e.AppointmentHanNhmid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("AppointmentHanNHMID");
            entity.Property(e => e.BelongTo)
                .HasMaxLength(10)
                .IsUnicode(false);
            entity.Property(e => e.CollectedBy).HasMaxLength(100);
            entity.Property(e => e.CollectedDate).HasColumnType("datetime");
            entity.Property(e => e.SampleType).HasMaxLength(50);
            entity.Property(e => e.Status).HasMaxLength(50);
            entity.Property(e => e.StorageLocation).HasMaxLength(500);
            entity.Property(e => e.StorageTemperature).HasColumnType("decimal(5, 2)");

            entity.HasOne(d => d.AppointmentHanNhm).WithMany(p => p.SamplePhuongMts)
                .HasForeignKey(d => d.AppointmentHanNhmid)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK__SamplePhu__Appoi__3B75D760");

            entity.HasOne(d => d.BelongToNavigation).WithMany(p => p.SamplePhuongMts)
                .HasForeignKey(d => d.BelongTo)
                .HasConstraintName("FK__SamplePhu__Belon__3C69FB99");
        });

        modelBuilder.Entity<SystemUserAccount>(entity =>
        {
            entity.HasKey(e => e.UserAccountId);

            entity.ToTable("System.UserAccount");

            entity.Property(e => e.UserAccountId).HasColumnName("UserAccountID");
            entity.Property(e => e.ApplicationCode).HasMaxLength(50);
            entity.Property(e => e.CreatedBy).HasMaxLength(50);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Email)
                .IsRequired()
                .HasMaxLength(150);
            entity.Property(e => e.EmployeeCode)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.FullName)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.ModifiedBy).HasMaxLength(50);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Password)
                .IsRequired()
                .HasMaxLength(100);
            entity.Property(e => e.Phone)
                .IsRequired()
                .HasMaxLength(50);
            entity.Property(e => e.RequestCode).HasMaxLength(50);
            entity.Property(e => e.UserName)
                .IsRequired()
                .HasMaxLength(50);

            entity.HasOne(d => d.Role).WithMany(p => p.SystemUserAccounts)
                .HasForeignKey(d => d.RoleId)
                .HasConstraintName("FK_UserAccount_UserRole");
        });

        modelBuilder.Entity<TestParticipantsHauHn>(entity =>
        {
            entity.HasKey(e => e.ParticipantHauHnid).HasName("PK__TestPart__E46A6AD2420EDCAB");

            entity.ToTable("TestParticipantsHauHN");

            entity.Property(e => e.ParticipantHauHnid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("ParticipantHauHNID");
            entity.Property(e => e.Address).HasMaxLength(255);
            entity.Property(e => e.DateOfBirth).HasColumnType("datetime");
            entity.Property(e => e.FullName).HasMaxLength(100);
            entity.Property(e => e.Gender).HasMaxLength(50);
            entity.Property(e => e.Idcard)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("IDCard");

            entity.HasMany(d => d.Tests).WithMany(p => p.ParticipantHauHns)
                .UsingEntity<Dictionary<string, object>>(
                    "TestParticipantsMapping",
                    r => r.HasOne<TestResultHauHn>().WithMany()
                        .HasForeignKey("TestId")
                        .HasConstraintName("FK__TestParti__TestI__48CFD27E"),
                    l => l.HasOne<TestParticipantsHauHn>().WithMany()
                        .HasForeignKey("ParticipantHauHnid")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("FK__TestParti__Parti__47DBAE45"),
                    j =>
                    {
                        j.HasKey("ParticipantHauHnid", "TestId").HasName("PK__TestPart__FCA659C2EED8686E");
                        j.ToTable("TestParticipantsMapping");
                        j.IndexerProperty<string>("ParticipantHauHnid")
                            .HasMaxLength(10)
                            .IsUnicode(false)
                            .HasColumnName("ParticipantHauHNID");
                        j.IndexerProperty<string>("TestId")
                            .HasMaxLength(10)
                            .IsUnicode(false)
                            .HasColumnName("TestID");
                    });
        });

        modelBuilder.Entity<TestResultDetailsHauHn>(entity =>
        {
            entity.HasKey(e => e.TestResultDetailHauHnid).HasName("PK__TestResu__4D68C48CE376A0AD");

            entity.ToTable("TestResultDetailsHauHN");

            entity.Property(e => e.TestResultDetailHauHnid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("TestResultDetailHauHNID");
            entity.Property(e => e.Locus).HasMaxLength(50);
            entity.Property(e => e.ResultHauHnid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("ResultHauHNID");
            entity.Property(e => e.SamplePhuongMtid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("SamplePhuongMTID");
            entity.Property(e => e.Value).HasMaxLength(50);

            entity.HasOne(d => d.ResultHauHn).WithMany(p => p.TestResultDetailsHauHns)
                .HasForeignKey(d => d.ResultHauHnid)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK__TestResul__Resul__4BAC3F29");

            entity.HasOne(d => d.SamplePhuongMt).WithMany(p => p.TestResultDetailsHauHns)
                .HasForeignKey(d => d.SamplePhuongMtid)
                .HasConstraintName("FK__TestResul__Sampl__4CA06362");
        });

        modelBuilder.Entity<TestResultHauHn>(entity =>
        {
            entity.HasKey(e => e.ResultHauHnid).HasName("PK__TestResu__8B21F9172A3C9544");

            entity.ToTable("TestResultHauHN");

            entity.Property(e => e.ResultHauHnid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("ResultHauHNID");
            entity.Property(e => e.AppointmentHanNhmid)
                .HasMaxLength(10)
                .IsUnicode(false)
                .HasColumnName("AppointmentHanNHMID");
            entity.Property(e => e.DiseaseRisk).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.GeneticMutation).HasMaxLength(255);
            entity.Property(e => e.RelationshipProbability).HasColumnType("decimal(5, 2)");
            entity.Property(e => e.ResultDate).HasColumnType("datetime");
            entity.Property(e => e.ResultValue).HasMaxLength(255);
            entity.Property(e => e.TechnicianId).HasColumnName("TechnicianID");
            entity.Property(e => e.TestDate).HasColumnType("datetime");
            entity.Property(e => e.TestType).HasMaxLength(50);

            entity.HasOne(d => d.AppointmentHanNhm).WithMany(p => p.TestResultHauHns)
                .HasForeignKey(d => d.AppointmentHanNhmid)
                .OnDelete(DeleteBehavior.Cascade)
                .HasConstraintName("FK__TestResul__Appoi__3F466844");

            entity.HasOne(d => d.Technician).WithMany(p => p.TestResultHauHnTechnicians)
                .HasForeignKey(d => d.TechnicianId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK__TestResul__Techn__403A8C7D");

            entity.HasOne(d => d.VerifiedByNavigation).WithMany(p => p.TestResultHauHnVerifiedByNavigations)
                .HasForeignKey(d => d.VerifiedBy)
                .HasConstraintName("FK__TestResul__Verif__412EB0B6");

            entity.HasMany(d => d.SamplePhuongMts).WithMany(p => p.ResultHauHns)
                .UsingEntity<Dictionary<string, object>>(
                    "TestResultSampleMapping",
                    r => r.HasOne<SamplePhuongMt>().WithMany()
                        .HasForeignKey("SamplePhuongMtid")
                        .OnDelete(DeleteBehavior.ClientSetNull)
                        .HasConstraintName("FK__TestResul__Sampl__44FF419A"),
                    l => l.HasOne<TestResultHauHn>().WithMany()
                        .HasForeignKey("ResultHauHnid")
                        .HasConstraintName("FK__TestResul__Resul__440B1D61"),
                    j =>
                    {
                        j.HasKey("ResultHauHnid", "SamplePhuongMtid").HasName("PK__TestResu__6C5E9C09AC45B546");
                        j.ToTable("TestResultSampleMapping");
                        j.IndexerProperty<string>("ResultHauHnid")
                            .HasMaxLength(10)
                            .IsUnicode(false)
                            .HasColumnName("ResultHauHNID");
                        j.IndexerProperty<string>("SamplePhuongMtid")
                            .HasMaxLength(10)
                            .IsUnicode(false)
                            .HasColumnName("SamplePhuongMTID");
                    });
        });

        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(e => e.RoleId).HasName("PK__UserRole__8AFACE1AAB80F223");

            entity.ToTable("UserRole");

            entity.Property(e => e.RoleId).ValueGeneratedNever();
            entity.Property(e => e.RoleName).HasMaxLength(10);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}