﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DNATesting.Repositories.HauHN.Models;

public partial class FeedbackHoangDn
{
    public string FeedbackHoangDnid { get; set; }

    public int UserAccountId { get; set; }

    public string OptionIdkhanhHn { get; set; }

    public string FeedbackText { get; set; }

    public int? Rating { get; set; }

    public string Response { get; set; }

    public bool? IsResolved { get; set; }

    public int? StaffId { get; set; }

    public DateTime? ResponseAt { get; set; }

    public bool? IsPublic { get; set; }

    public DateTime? FeedbackAt { get; set; }

    public virtual DnatestOptionKhanhHn OptionIdkhanhHnNavigation { get; set; }

    public virtual SystemUserAccount Staff { get; set; }

    public virtual SystemUserAccount UserAccount { get; set; }
}