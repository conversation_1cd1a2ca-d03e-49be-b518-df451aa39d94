﻿@page "/Account/Login"
@using DrugPrevention.Repositories.TuyenTM.Models
@using DrugPrevention.Services.TuyenTM
@using System.Security.Claims
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@attribute [AllowAnonymous]
@inject IServiceProviders _serviceProviders
@inject IHttpContextAccessor HttpContextAccessor
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthenticationStateProvider

<h3 class="text-danger flex-lg-wrap text-center ">Drug Prevention System</h3>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4 shadow p-4 rounded border">
            <h4 class="text-center mb-2">Sign In</h4>

            <div class="form-container">
                <div class="mb-3">
                    <label class="form-label">Username</label>
                    <input @bind="loginModel.UserName" @bind:event="oninput" class="form-control" type="text" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <input @bind="loginModel.Password" @bind:event="oninput" class="form-control" type="password" />
                </div>

                <button @onclick="HandleLogin" class="btn btn-outline-dark w-100 mt-2">Login</button>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="text-danger mt-2">@errorMessage</div>
                }
            </div>
        </div>
    </div>
</div>


@code {
    private LoginModel loginModel { get; set; } = new LoginModel();
    private string? errorMessage;

    private async Task HandleLogin()
    {
        try
        {
            if (string.IsNullOrWhiteSpace(loginModel.UserName) || string.IsNullOrWhiteSpace(loginModel.Password))
            {
                errorMessage = "Please enter both username and password";
                return;
            }

            var userAccount = await _serviceProviders.System_UserAccountService.GetUserAccount(loginModel.UserName, loginModel.Password);

            if (userAccount != null && userAccount.UserAccountID > 0 && userAccount.IsActive && !string.IsNullOrEmpty(userAccount.UserName))
            {
                var httpContext = HttpContextAccessor.HttpContext;
                if (httpContext != null)
                {
                    // Create claims exactly like ASM1
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.Name, loginModel.UserName),
                        new Claim(ClaimTypes.Role, userAccount.RoleId.ToString()),
                    };

                    var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                    await httpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(identity));

                    httpContext.Response.Cookies.Append("UserName", userAccount.UserName);

                    // Redirect to home page like ASM1
                    Navigation.NavigateTo("/", forceLoad: true);
                }
                else
                {
                    errorMessage = "Authentication context not available";
                }
            }
            else
            {
                errorMessage = "Login fail, please check your account";
                // Sign out any existing authentication like ASM1
                var httpContext = HttpContextAccessor.HttpContext;
                if (httpContext != null)
                {
                    await httpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Login error: {ex.Message}";
        }
    }

    private class LoginModel
    {
        public string UserName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }
}