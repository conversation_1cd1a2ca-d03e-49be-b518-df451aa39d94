@page "/Account/Login"
@using DrugPrevention.Repositories.TuyenTM.Models
@using DrugPrevention.Services.TuyenTM
@using System.Security.Claims
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@attribute [AllowAnonymous]
@inject IServiceProviders _serviceProviders
@inject IHttpContextAccessor HttpContextAccessor
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthenticationStateProvider

<h3 class="text-danger flex-lg-wrap text-center ">Drug Prevention System</h3>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4 shadow p-4 rounded border">
            <h4 class="text-center mb-2">Sign In</h4>

            <div class="form-container">
                <div class="mb-3">
                    <label class="form-label">Username</label>
                    <input @bind="loginModel.UserName" class="form-control" type="text" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Password</label>
                    <input @bind="loginModel.Password" class="form-control" type="password" />
                </div>

                <button @onclick="HandleLogin" class="btn btn-outline-dark w-100 mt-2">Log In</button>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="text-danger mt-2">@errorMessage</div>
                }
            </div>
        </div>
    </div>
</div>


@code {
    private LoginModel loginModel { get; set; } = new LoginModel();
    private string? errorMessage;

    private async Task HandleLogin()
    {
        var userAccount = await _serviceProviders.System_UserAccountService.GetUserAccount(loginModel.UserName, loginModel.Password);

        if (userAccount != null && userAccount.UserAccountID > 0 && userAccount.IsActive)
        {
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Name, loginModel.UserName),
                new Claim(ClaimTypes.Role, userAccount.RoleId.ToString()),
                new Claim("FullName", userAccount.FullName ?? userAccount.UserName),
                new Claim("Email", userAccount.Email ?? ""),
                new Claim("UserAccountID", userAccount.UserAccountID.ToString())
            };

            var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
            var principal = new ClaimsPrincipal(identity);

            await HttpContextAccessor.HttpContext.SignInAsync(
                CookieAuthenticationDefaults.AuthenticationScheme,
                principal,
                new AuthenticationProperties
                {
                    IsPersistent = true,
                    ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(30)
                });

            // Set cookie for username (similar to ASM1)
            HttpContextAccessor.HttpContext.Response.Cookies.Append("UserName", userAccount.UserName);

            // Redirect to home page
            Navigation.NavigateTo("/", forceLoad: true);
        }
        else
        {
            errorMessage = "Login fail, please check your account";
        }
    }

    private class LoginModel
    {
        public string UserName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }
}