﻿using DNATesting.Repositories.PhuongMT;
using DNATesting.Repositories.PhuongMT.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.PhuongMT
{
    public class TestParticipantsHauHNServices : ITestParticipantsHauHNServices
    {
        private readonly TestParticipantsHauHnRepository _repository;

        public TestParticipantsHauHNServices() => _repository ??= new TestParticipantsHauHnRepository();

        public async Task<List<TestParticipantsHauHn>> GetAllAsync()
        {
            return await _repository.GetAllAsync();
        }
    }
}
