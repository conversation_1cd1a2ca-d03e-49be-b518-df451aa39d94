﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace DNATesting.Repositories.PhuongMT.Models;

public partial class SystemUserAccount
{
    public int UserAccountId { get; set; }

    public string UserName { get; set; }

    public string Password { get; set; }

    public string FullName { get; set; }

    public string Email { get; set; }

    public string Phone { get; set; }

    public string EmployeeCode { get; set; }

    public int RoleId { get; set; }

    public string RequestCode { get; set; }

    public DateTime? CreatedDate { get; set; }

    public string ApplicationCode { get; set; }

    public string CreatedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public string ModifiedBy { get; set; }

    public bool IsActive { get; set; }

    public virtual ICollection<AppointmentDetailHanNhm> AppointmentDetailHanNhmTechnicians { get; set; } = new List<AppointmentDetailHanNhm>();

    public virtual ICollection<AppointmentDetailHanNhm> AppointmentDetailHanNhmUserAccounts { get; set; } = new List<AppointmentDetailHanNhm>();

    public virtual ICollection<AppointmentHanNhm> AppointmentHanNhms { get; set; } = new List<AppointmentHanNhm>();

    public virtual ICollection<DnatestOptionDetailKhanhHn> DnatestOptionDetailKhanhHnCreatByNavigations { get; set; } = new List<DnatestOptionDetailKhanhHn>();

    public virtual ICollection<DnatestOptionDetailKhanhHn> DnatestOptionDetailKhanhHnUpdateByNavigations { get; set; } = new List<DnatestOptionDetailKhanhHn>();

    public virtual ICollection<DnatestOptionKhanhHn> DnatestOptionKhanhHnCreatByNavigations { get; set; } = new List<DnatestOptionKhanhHn>();

    public virtual ICollection<DnatestOptionKhanhHn> DnatestOptionKhanhHnUpdateByNavigations { get; set; } = new List<DnatestOptionKhanhHn>();

    public virtual ICollection<FeedbackHoangDn> FeedbackHoangDnStaffs { get; set; } = new List<FeedbackHoangDn>();

    public virtual ICollection<FeedbackHoangDn> FeedbackHoangDnUserAccounts { get; set; } = new List<FeedbackHoangDn>();

    public virtual ICollection<ProcessLogPhuongMt> ProcessLogPhuongMts { get; set; } = new List<ProcessLogPhuongMt>();

    public virtual ICollection<ReportTinPt> ReportTinPts { get; set; } = new List<ReportTinPt>();

    public virtual UserRole Role { get; set; }

    public virtual ICollection<TestResultHauHn> TestResultHauHnTechnicians { get; set; } = new List<TestResultHauHn>();

    public virtual ICollection<TestResultHauHn> TestResultHauHnVerifiedByNavigations { get; set; } = new List<TestResultHauHn>();
}