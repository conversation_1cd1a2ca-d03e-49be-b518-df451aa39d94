﻿@page "/"
@using DNATesting.Repositories.HauHN.Models
@inject IJSRuntime JSRuntime
@using System.Security.Claims

<PageTitle>DNA Testing System - Trang chủ</PageTitle>

<div class="home-container">
    <div class="hero-section">
        <div class="hero-content">
            @if (user != null)
            {
                <div class="user-welcome mb-4">
                    <h2>
                        <i class="bi bi-person-circle"></i>
                        <PERSON><PERSON> chào, @user.Identity.Name!
                    </h2>
                    <p class="user-role">Vai trò: @(user?.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Role)?.Value ?? "Người dùng")</p>
                </div>
            }
            <h1>
                <i class="bi bi-dna"></i>
                DNA Testing System
            </h1>
            <p class="lead">Hệ thống quản lý xét nghiệm DNA chuyên nghiệp</p>
            @if (user == null)
            {
                <div class="mt-4">
                    <a href="/Account/Login" class="btn btn-light btn-lg">
                        <i class="bi bi-box-arrow-in-right"></i>
                        Đăng nhập để tiếp tục
                    </a>
                </div>
            }
        </div>
    </div>

    <div class="features-section">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-clipboard2-pulse"></i>
                        </div>
                        <h3>Quản lý kết quả</h3>
                        <p>Theo dõi và quản lý kết quả xét nghiệm DNA một cách hiệu quả</p>
                        <a href="/TestResultHauHNs/TestResultHauHnList" class="btn btn-primary">
                            Xem kết quả
                            <i class="bi bi-arrow-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <h3>Phân tích dữ liệu</h3>
                        <p>Phân tích và đánh giá kết quả với độ chính xác cao</p>
                        <a href="#" class="btn btn-primary">
                            Xem thống kê
                            <i class="bi bi-arrow-right"></i>
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <h3>Bảo mật thông tin</h3>
                        <p>Đảm bảo an toàn và bảo mật thông tin khách hàng</p>
                        <a href="#" class="btn btn-primary">
                            Tìm hiểu thêm
                            <i class="bi bi-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3">
                    <div class="stat-card">
                        <i class="bi bi-people"></i>
                        <h4>1,000+</h4>
                        <p>Khách hàng</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <i class="bi bi-clipboard-data"></i>
                        <h4>5,000+</h4>
                        <p>Xét nghiệm</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <i class="bi bi-award"></i>
                        <h4>99.9%</h4>
                        <p>Độ chính xác</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <i class="bi bi-hospital"></i>
                        <h4>50+</h4>
                        <p>Đối tác</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .home-container {
        background-color: #f8f9fa;
    }

    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 6rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .hero-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(255,255,255,0.1)" fill-opacity="1" d="M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,213.3C672,192,768,128,864,128C960,128,1056,192,1152,208C1248,224,1344,192,1392,176L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
        background-position: bottom;
        background-repeat: no-repeat;
        opacity: 0.1;
        z-index: 0;
    }

    .hero-content {
        position: relative;
        z-index: 1;
    }

    .hero-section h1 {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .hero-section .lead {
        font-size: 1.5rem;
        opacity: 0.9;
    }

    .features-section {
        padding: 5rem 0;
        background: white;
    }

    .feature-card {
        background: white;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        height: 100%;
    }

    .feature-card:hover {
        transform: translateY(-10px);
    }

    .feature-icon {
        font-size: 3rem;
        color: #667eea;
        margin-bottom: 1.5rem;
    }

    .feature-card h3 {
        color: #2d3748;
        margin-bottom: 1rem;
    }

    .feature-card p {
        color: #718096;
        margin-bottom: 1.5rem;
    }

    .feature-card .btn {
        border-radius: 50px;
        padding: 0.5rem 1.5rem;
    }

    .feature-card .btn i {
        margin-left: 0.5rem;
        transition: transform 0.3s ease;
    }

    .feature-card .btn:hover i {
        transform: translateX(5px);
    }

    .stats-section {
        background: #2d3748;
        color: white;
        padding: 4rem 0;
    }

    .stat-card {
        text-align: center;
        padding: 2rem;
    }

    .stat-card i {
        font-size: 2.5rem;
        color: #667eea;
        margin-bottom: 1rem;
    }

    .stat-card h4 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stat-card p {
        color: #a0aec0;
        font-size: 1.1rem;
        margin: 0;
    }

    @@media (max-width: 768px) {
        .hero-section {
            padding: 4rem 1rem;
        }

        .hero-section h1 {
            font-size: 2.5rem;
        }

        .hero-section .lead {
            font-size: 1.2rem;
        }

        .feature-card {
            margin-bottom: 2rem;
        }

        .stat-card {
            margin-bottom: 2rem;
        }
    }

    .user-welcome h2 {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .user-role {
        font-size: 1.1rem;
        opacity: 0.8;
        margin: 0;
    }
</style>

@code {
    private ClaimsPrincipal user;
    protected override async Task OnInitializedAsync()
    {
        var authState = await authenticationStateProvider.GetAuthenticationStateAsync();
        user = authState.User;

        if (!user.Identity.IsAuthenticated)
        {
            navigationManager.NavigateTo("/Account/Login", true);
        }
    }
}
