﻿using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.PhuongMT
{
    public class ServiceProviders : IServiceProviders
    {
        private TestParticipantsHauHNServices _testParticipantsHauHNServices;
        private SamplePhuongMTService _samplePhuongMTService;
        private AppointmentHanNHMService _appointmentHanNHMService;
        private SystemUserAccountService _userAccountService;

        public ServiceProviders() { }

        public IAppointmentHanNHMService appointmentHanNHMServices
        {
            get
            {
                return _appointmentHanNHMService ??= new AppointmentHanNHMService();
            }
        }

        public ISamplePhuongMTService samplePhuongMTService
        {
            get
            {
                return _samplePhuongMTService ??= new SamplePhuongMTService();
            }
        }

        public ITestParticipantsHauHNServices testParticipantsHauHNservices
        {
            get
            {
                return _testParticipantsHauHNServices ??= new TestParticipantsHauHNServices();
            }
        }

        public ISystemUserAccountService systemUserAccountService
        {
            get
            {
                return _userAccountService ??= new SystemUserAccountService();
            }
        }
    }

    public interface IServiceProviders
    {

        IAppointmentHanNHMService appointmentHanNHMServices { get; }
        ISamplePhuongMTService samplePhuongMTService { get; }
        ITestParticipantsHauHNServices testParticipantsHauHNservices { get; }
        ISystemUserAccountService systemUserAccountService { get; }


    }
}
