﻿using DNATesting.Repositories.PhuongMT;
using DNATesting.Repositories.PhuongMT.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.PhuongMT
{
    public class AppointmentHanNHMService : IAppointmentHanNHMService
    {
        private readonly AppointmentHanNHMRepository _repository ;

        public AppointmentHanNHMService() => _repository ??= new AppointmentHanNHMRepository();
        public async Task<List<AppointmentHanNhm>> GetAllAsync()
        {
            return await _repository.GetAllAsync();
        }
    }
}
