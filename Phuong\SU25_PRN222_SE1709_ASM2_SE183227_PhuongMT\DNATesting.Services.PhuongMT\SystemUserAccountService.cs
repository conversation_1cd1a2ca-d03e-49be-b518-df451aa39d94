﻿using DNATesting.Repositories.PhuongMT;
using DNATesting.Repositories.PhuongMT.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DNATesting.Services.PhuongMT
{
    public class SystemUserAccountService : ISystemUserAccountService
    {
        private readonly SystemUserAccountRepository _repository;

        public SystemUserAccountService() => _repository = new SystemUserAccountRepository();

        public async Task<List<SystemUserAccount>> GetAllAccount()
        {
            return await _repository.GetAllAsync();
        }

        public async Task<SystemUserAccount> GetUserAccount(string username, string pass)
        {
            return await _repository.GetUserAccount(username, pass);
        }

        public async Task<(bool IsSuccess, string ErrorMessage)> LoginAsync(string email, string password)
        {
            var user = await _repository.GetUserAccount(email, password);

            if (user != null)
            {
                return (true, string.Empty);
            }
            return (false, "Invalid email or password.");
        }

        public async Task LogoutAsync()
        {
            throw new NotImplementedException();
        }
    }
}
